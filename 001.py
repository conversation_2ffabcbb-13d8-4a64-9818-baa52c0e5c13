import requests
from requests.adapters import HTTPAdapter
from urllib.parse import urlenco<PERSON>, quote
from urllib3.util.retry import Retry

# url = "https://courseapi.ulearning.cn/users/login/v2"

# headers = {
#     "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
#     "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
#     "Accept-Encoding": "gzip, deflate, br, zstd",
#     "Content-Type": "application/x-www-form-urlencoded",
#     "Pragma": "no-cache",
#     "Cache-Control": "no-cache",
#     "sec-ch-ua": '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
#     "sec-ch-ua-mobile": "?0",
#     "sec-ch-ua-platform": '"macOS"',
#     "Origin": "https://umooc.ulearning.cn",
#     "Upgrade-Insecure-Requests": "1",
#     "Sec-Fetch-Site": "same-site",
#     "Sec-Fetch-Mode": "navigate",
#     "Sec-Fetch-User": "?1",
#     "Sec-Fetch-Dest": "document",
#     "Referer": "https://umooc.ulearning.cn/",
#     "Accept-Language": "zh-CN,zh;q=0.9",
# }

# cookies = {
#     "loginFrom": "https://umooc.ulearning.cn/pc.html#/login",
#     "AUTHORIZATION": "DD4BAD53E024A7DC79B775B6D57AD0D9",
#     "token": "DD4BAD53E024A7DC79B775B6D57AD0D9",
#     "lms_user_org": "3277",
#     "USER_INFO": quote('{"orgName":"武汉工商学院","orgLogo":"https://leicloud.ulearning.cn/resource/3277/591257/6d49103f-0a4c-4ac1-8ba9-c8a78e4263b1.png","roleId":9,"sex":"0","orgHome":"whgs.ulearning.cn","userId":13542305,"orgId":3277,"authorization":"DD4BAD53E024A7DC79B775B6D57AD0D9","studentId":"24206101","loginName":"wtbu24206101","name":"秦甜甜","uversion":2}'),
#     "USERINFO": quote('{"orgName":"武汉工商学院","orgLogo":"https://leicloud.ulearning.cn/resource/3277/591257/6d49103f-0a4c-4ac1-8ba9-c8a78e4263b1.png","roleId":9,"sex":"0","orgHome":"whgs.ulearning.cn","userId":13542305,"orgId":3277,"authorization":"DD4BAD53E024A7DC79B775B6D57AD0D9","studentId":"24206101","loginName":"wtbu24206101","name":"秦甜甜","uversion":2}'),
#     "lang": "zh",
#     "ROOT_ORIGIN": "www.ulearning.cn",
#     "_abfpc": "5a9f3122e2cf111ae2ec562734f25708d934a775_2.0",
#     "cna": "6536ef29902f7af47f4e28c7c3de1615",
#     "isClassroom": "1",
#     "belong": "null",
#     "xn_dvid_kf_20125": "08E8CC-E785248E-58F1-73D7-BD56-C42DBD2FCE7B",
#     "xn_sid_kf_20125": "1759932956181824",
# }

# data = {
#     "loginName": "wtbu24206101",
#     "password": "qttQTT20030526",
# }




# response = requests.post(url, headers=headers, cookies=cookies, data=data)
# response.encoding = 'utf-8'
# # 输出响应信息
# print(f"状态码: {response.status_code}")
# print(f"Cookies: {response.cookies}")
# print(f"响应头: {response.headers}")
# # print(f"响应内容: {response.text}")


# a = quote('{"orgName":"武汉工商学院","orgLogo":"https://leicloud.ulearning.cn/resource/3277/591257/6d49103f-0a4c-4ac1-8ba9-c8a78e4263b1.png","roleId":9,"sex":"0","orgHome":"whgs.ulearning.cn","userId":13542305,"orgId":3277,"authorization":"DD4BAD53E024A7DC79B775B6D57AD0D9","studentId":"24206101","loginName":"wtbu24206101","name":"秦甜甜","uversion":2}')
# print(a)


# curl -i -X POST 'https://courseapi.ulearning.cn/users/login/v2' -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' -H 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7' -H 'Accept-Encoding: gzip, deflate, br, zstd' -H 'Content-Type: application/x-www-form-urlencoded' -H 'Pragma: no-cache' -H 'Cache-Control: no-cache' -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' -H 'sec-ch-ua-mobile: ?0' -H 'sec-ch-ua-platform: "macOS"' -H 'Origin: https://umooc.ulearning.cn' -H 'Upgrade-Insecure-Requests: 1' -H 'Sec-Fetch-Site: same-site' -H 'Sec-Fetch-Mode: navigate' -H 'Sec-Fetch-User: ?1' -H 'Sec-Fetch-Dest: document' -H 'Referer: https://umooc.ulearning.cn/' -H 'Accept-Language: zh-CN,zh;q=0.9' -H 'Cookie: loginFrom=https%3A%2F%2Fumooc.ulearning.cn%2Fpc.html%23%2Flogin; AUTHORIZATION=DD4BAD53E024A7DC79B775B6D57AD0D9; token=DD4BAD53E024A7DC79B775B6D57AD0D9; lms_user_org=3277; USER_INFO=%7B%22orgName%22%3A%22%u6B66%u6C49%u5DE5%u5546%u5B66%u9662%22%2C%22orgLogo%22%3A%22https%3A%2F%2Fleicloud.ulearning.cn%2Fresource%2F3277%2F591257%2F6d49103f-0a4c-4ac1-8ba9-c8a78e4263b1.png%22%2C%22roleId%22%3A9%2C%22sex%22%3A%220%22%2C%22orgHome%22%3A%22whgs.ulearning.cn%22%2C%22userId%22%3A13542305%2C%22orgId%22%3A3277%2C%22authorization%22%3A%22DD4BAD53E024A7DC79B775B6D57AD0D9%22%2C%22studentId%22%3A%2224206101%22%2C%22loginName%22%3A%22wtbu24206101%22%2C%22name%22%3A%22%u79E6%u751C%u751C%22%2C%22uversion%22%3A2%7D; USERINFO=%7B%22orgName%22%3A%22%E6%AD%A6%E6%B1%89%E5%B7%A5%E5%95%86%E5%AD%A6%E9%99%A2%22%2C%22orgLogo%22%3A%22https%3A%2F%2Fleicloud.ulearning.cn%2Fresource%2F3277%2F591257%2F6d49103f-0a4c-4ac1-8ba9-c8a78e4263b1.png%22%2C%22roleId%22%3A9%2C%22sex%22%3A%220%22%2C%22orgHome%22%3A%22whgs.ulearning.cn%22%2C%22userId%22%3A13542305%2C%22orgId%22%3A3277%2C%22authorization%22%3A%22DD4BAD53E024A7DC79B775B6D57AD0D9%22%2C%22studentId%22%3A%2224206101%22%2C%22loginName%22%3A%22wtbu24206101%22%2C%22name%22%3A%22%E7%A7%A6%E7%94%9C%E7%94%9C%22%2C%22uversion%22%3A2%7D; lang=zh; ROOT_ORIGIN=www.ulearning.cn; _abfpc=5a9f3122e2cf111ae2ec562734f25708d934a775_2.0; cna=6536ef29902f7af47f4e28c7c3de1615; isClassroom=1; belong=null; xn_dvid_kf_20125=08E8CC-E785248E-58F1-73D7-BD56-C42DBD2FCE7B; xn_sid_kf_20125=1759932956181824' --data-urlencode 'loginName=wtbu24206101' --data-urlencode 'password=qttQTT20030526' --http1.1



import requests
from urllib.parse import urlencode

url = "https://courseapi.ulearning.cn/users/login/v2"

headers = {
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Content-Type": "application/x-www-form-urlencoded",
    "Pragma": "no-cache",
    "Cache-Control": "no-cache",
    "sec-ch-ua": '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"macOS"',
    "Origin": "https://umooc.ulearning.cn",
    "Upgrade-Insecure-Requests": "1",
    "Sec-Fetch-Site": "same-site",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-User": "?1",
    "Sec-Fetch-Dest": "document",
    "Referer": "https://umooc.ulearning.cn/",
    "Accept-Language": "zh-CN,zh;q=0.9",
}

# 使用与curl完全相同的cookie值（已编码）
cookies = {
    "loginFrom": "https%3A%2F%2Fumooc.ulearning.cn%2Fpc.html%23%2Flogin",
    "AUTHORIZATION": "DD4BAD53E024A7DC79B775B6D57AD0D9",
    "token": "DD4BAD53E024A7DC79B775B6D57AD0D9",
    "lms_user_org": "3277",
    # 直接使用curl中的USER_INFO编码值（Unicode编码）
    "USER_INFO": "%7B%22orgName%22%3A%22%u6B66%u6C49%u5DE5%u5546%u5B66%u9662%22%2C%22orgLogo%22%3A%22https%3A%2F%2Fleicloud.ulearning.cn%2Fresource%2F3277%2F591257%2F6d49103f-0a4c-4ac1-8ba9-c8a78e4263b1.png%22%2C%22roleId%22%3A9%2C%22sex%22%3A%220%22%2C%22orgHome%22%3A%22whgs.ulearning.cn%22%2C%22userId%22%3A13542305%2C%22orgId%22%3A3277%2C%22authorization%22%3A%22DD4BAD53E024A7DC79B775B6D57AD0D9%22%2C%22studentId%22%3A%2224206101%22%2C%22loginName%22%3A%22wtbu24206101%22%2C%22name%22%3A%22%u79E6%u751C%u751C%22%2C%22uversion%22%3A2%7D",
    # 直接使用curl中的USERINFO编码值（UTF-8编码）
    "USERINFO": "%7B%22orgName%22%3A%22%E6%AD%A6%E6%B1%89%E5%B7%A5%E5%95%86%E5%AD%A6%E9%99%A2%22%2C%22orgLogo%22%3A%22https%3A%2F%2Fleicloud.ulearning.cn%2Fresource%2F3277%2F591257%2F6d49103f-0a4c-4ac1-8ba9-c8a78e4263b1.png%22%2C%22roleId%22%3A9%2C%22sex%22%3A%220%22%2C%22orgHome%22%3A%22whgs.ulearning.cn%22%2C%22userId%22%3A13542305%2C%22orgId%22%3A3277%2C%22authorization%22%3A%22DD4BAD53E024A7DC79B775B6D57AD0D9%22%2C%22studentId%22%3A%2224206101%22%2C%22loginName%22%3A%22wtbu24206101%22%2C%22name%22%3A%22%E7%A7%A6%E7%94%9C%E7%94%9C%22%2C%22uversion%22%3A2%7D",
    "lang": "zh",
    "ROOT_ORIGIN": "www.ulearning.cn",
    "_abfpc": "5a9f3122e2cf111ae2ec562734f25708d934a775_2.0",
    "cna": "6536ef29902f7af47f4e28c7c3de1615",
    "isClassroom": "1",
    "belong": "null",
    "xn_dvid_kf_20125": "08E8CC-E785248E-58F1-73D7-BD56-C42DBD2FCE7B",
    "xn_sid_kf_20125": "1759932956181824",
}

data = {
    "loginName": "wtbu24206101",
    "password": "qttQTT20030526",
}

# 创建session以更好地模拟浏览器行为
session = requests.Session()

# 添加一些额外的配置来更好地模拟curl
session.headers.update({
    'Connection': 'keep-alive',
})

try:
    # 发送请求，添加一些curl的默认行为
    response = session.post(
        url, 
        headers=headers, 
        cookies=cookies, 
        data=data,
        timeout=30,
        allow_redirects=True,  # 允许重定向
        verify=True,  # 验证SSL证书
    )
    
    response.encoding = 'utf-8'
    
    # 输出响应信息
    print(f"状态码: {response.status_code}")
    print(f"最终URL: {response.url}")
    print(f"响应Cookies: {dict(response.cookies)}")
    print(f"响应头: {dict(response.headers)}")
    
    # 检查是否有新的cookies
    if response.cookies:
        print("\n=== 新获取的Cookies ===")
        for cookie in response.cookies:
            print(f"{cookie.name}: {cookie.value}")
    else:
        print("\n=== 没有获取到新的Cookies ===")
    
    # 检查响应内容（可选，取消注释查看）
    # print(f"响应内容: {response.text}")
    
    # 分析登录状态
    if response.status_code == 200:
        if "courseList" in response.url or "index" in response.url:
            print("\n✅ 登录成功 - 已重定向到课程页面")
        elif response.cookies:
            print("\n✅ 可能登录成功 - 获取到新的cookies")
        else:
            print("\n❓ 登录状态不明确")
    else:
        print(f"\n❌ 请求失败 - HTTP状态码: {response.status_code}")

except requests.exceptions.RequestException as e:
    print(f"❌ 请求异常: {e}")
except Exception as e:
    print(f"❌ 其他错误: {e}")

print("\n=== 关键差异说明 ===")
print("1. loginFrom 现在使用URL编码格式")
print("2. USER_INFO 使用curl中的Unicode编码 (%u6B66)")
print("3. USERINFO 使用curl中的UTF-8编码 (%E6%AD%A6)")
print("4. 使用Session来保持连接状态")
print("5. 添加了超时和重定向处理")
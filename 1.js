import { n, ao as k, ap as x, dD as S, dE as I, aq as g, ar as $, as as F, at as T, _, au as L } from "./index-6623cdb9.js"; import { C as p } from "./clipboard-80022da2.js"; import m from "./tileButtonDialog-1612c9f4.js"; import { g as N, m as h } from "./orderOld-36e43033.js"; import { s as P } from "./activity-f0e1a07d.js"; import { s as A } from "./index-d9f2aff0.js"; import { s as O } from "./common-a76ac41a.js"; import { h as j } from "./headRebate-9b4a8711.js"; import { c as R } from "./index-9abfb5ce.js"; import { w as E } from "./hirePopup-3be2e2e8.js"; import { i as G } from "./companyInfo-ed30b2c6.js"; const B = { name: "Hello", components: { tileButtonDialog: m }, props: ["orderData"], data() { return { title: "这是一个测试的数据标题", imgUrl: "https://osscdn.lbdj.com/", imgBg: !1, images: [], index: 0, showCk: !1, logistics: "", logisticsNum: 0, logisticsInfo: "", showDr: !1, isShowAddressTipDialog: !1, ischildren: 1 } }, created() { let e; this.orderData.logisticsSingle && (e = this.orderData.logisticsSingle.replace(/\，/g, ",").replace(/\s*/g, "").split(","), this.logisticsNum = e.length, this.logistics = e) }, mounted() { const e = document.getElementById("parent-element"); e && e.children ? this.ischildren = e.children.length : this.ischildren = !1 }, methods: { goPath() { this.$router.push({ path: "/jumpMapH5", query: { start: this.orderData.pickupLocation, dest: this.orderData.clientLocation } }) }, queryLogistics(e) { this.$parent.queryLogistics(e) }, lookImg() { this.images = this.orderData.packageImgsList, this.imgBg = !0 }, openWl() { this.orderData.sameDayLogList && this.orderData.sameDayLogList.length > 0 && (this.showDr = !0, this.logisticsInfo = this.orderData.sameDayLogList) }, async goGps(e) { try { const t = await N({ address: e }); if (t.code == 200) { const s = t.data.geocodes[0].location.split(","); console.log(s, 777), this.isMiniwx && this.$router.push({ path: "/orderHome/visitSign/distance", query: { residentLocation: t.data.geocodes[0].location } }); const i = { reqType: "GET_WEB_USER_INFO_LOCATION", data: { latitude: parseFloat(s[1]), longitude: parseFloat(s[0]), address: e, description: "导航" } }; window.sendAppData(i) } else this.$toast.fail("地址错误") } catch (t) { this.$toast.fail("地址错误") } }, goLogDet() { this.$router.push({ path: "/orderHome/arrivaldetail", query: { orderId: this.orderData.id, orderSn: this.orderData.orderSn } }) }, goLogDebang() { this.$router.push({ path: "/orderHome/logisticsDetails", query: { orderId: this.orderData.id } }) }, goPhone(e) { if (this.isMiniwx) window.location.href = "tel:" + e; else { const t = { reqType: "GET_WEB_USER_INFO_PHONE", data: { phone: e } }; window.sendAppData(t) } }, copyOrderButton() { const e = this, t = new p(".copy-btn"); t.on("success", () => { e.$toast.success("收货地址已复制"), t.destroy() }), t.on("error", () => { e.$toast.fail("该浏览器不支持自动复制"), t.destroy() }) }, copyLogisticsSingle() { const e = this, t = new p(".copy-btn4"); t.on("success", () => { e.$toast.success("物流单号已复制"), t.destroy() }), t.on("error", () => { e.$toast.fail("该浏览器不支持自动复制"), t.destroy() }) } } }; var q = function () { var t = this, s = t._self._c; return s("div", { staticClass: "main" }, [t.orderData.isPS == 1 && t.orderData.cid != "130" ? s("div", { staticClass: "new-padd new-main-2021" }, [s("div", { staticClass: "new-title" }, [s("div", { staticClass: "new-title-left" }, [t._v("物流详情")]), s("div", { staticClass: "new-title-right" }, [s("div", { staticClass: "img", on: { click: function (i) { return t.goGps(t.orderData.pickupAddress) } } }, [s("img", { attrs: { src: t.imgUrl + "lbdj_app_h5/newUi/2021/icon_map.png" } })])])]), s("van-divider"), s("div", { staticClass: "new-list flex-list" }, [t.orderData.agentPay ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-name" }, [t._v("代付运费")]), s("div", { staticClass: "li-value" }, [t._v(" " + t._s(t.orderData.agentPay) + " ")])]) : t._e(), t.orderData.logisticsCompany ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-name" }, [t._v("物流公司")]), s("div", { staticClass: "li-value" }, [t._v(" " + t._s(t.orderData.logisticsCompany) + " ")])]) : t._e(), t.orderData.logisticsSingle ? s("div", { staticClass: "list-li", staticStyle: { "justify-content": "space-between" } }, [s("div", { staticClass: "li-name" }, [t._v("物流单号")]), s("div", { staticClass: "li-value", staticStyle: { display: "block", "text-align": "right" } }, t._l(t.logistics, function (i) { return t.logistics && t.logistics.length < 2 ? s("div", { staticStyle: { margin: "0.15rem 0" } }, [s("span", { attrs: { id: "copy_texts" } }, [t._v(t._s(i))]), t.orderData.states <= 5 && t.orderData.states >= 3 ? s("van-tag", { staticClass: "copy-btn", attrs: { plain: "", type: "primary" }, on: { click: function (a) { return t.queryLogistics(i) } } }, [t._v("查询")]) : t._e()], 1) : t._e() }), 0)]) : t._e(), t.logistics && t.logistics.length > 1 ? s("div", [t._l(t.logistics, function (i) { return s("div", { staticStyle: { float: "left", margin: "0.15rem 0", "margin-right": "0.4rem" } }, [s("span", { attrs: { id: "copy_texts" } }, [t._v(t._s(i))]), t.orderData.states <= 5 && t.orderData.states >= 3 ? s("van-tag", { staticClass: "copy-btn", attrs: { plain: "", type: "primary" }, on: { click: function (a) { return t.queryLogistics(i) } } }, [t._v("查询")]) : t._e()], 1) }), s("div", { staticStyle: { clear: "both" } })], 2) : t._e(), t.orderData.pickupPassword ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-name" }, [t._v("提货密码")]), s("div", { staticClass: "li-value" }, [t._v(" " + t._s(t.orderData.pickupPassword) + " ")])]) : t._e(), t.orderData.pickupPhone ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-name" }, [t._v("提货电话")]), s("div", { staticClass: "li-value" }, [s("span", { attrs: { id: "copy_texts" } }, [t._v(t._s(t.orderData.pickupPhone))]), s("van-tag", { staticClass: "copy-btn", attrs: { plain: "", type: "primary" }, on: { click: function (i) { return t.goPhone(t.orderData.pickupPhone) } } }, [t._v("拨打")])], 1)]) : t._e(), t.orderData.goods ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-name" }, [t._v("货物数量")]), s("div", { staticClass: "li-value" }, [t._v(" " + t._s(t.orderData.goods) + " ")])]) : t._e(), t.orderData.volumeOfGoods ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-name" }, [t._v("货物立方")]), s("div", { staticClass: "li-value" }, [t._v(" " + t._s(t.orderData.volumeOfGoods) + " ")])]) : t._e(), t.orderData.pickupAddress ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-name" }, [t._v("提货地址")]), s("div", { staticClass: "li-value" }, [s("span", [t._v(t._s(t.orderData.pickupAddress))]), t.orderData.popPickupAddress ? s("van-icon", { staticStyle: { color: "#cbcbcb" }, attrs: { name: "question-o" }, on: { click: function (i) { t.isShowAddressTipDialog = !0 } } }) : t._e()], 1)]) : t._e(), t.orderData.distance ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-name" }, [t._v("配送距离")]), s("div", { staticClass: "li-value" }, [s("span", { attrs: { id: "copy_texts" } }, [t._v(t._s(t.orderData.distance) + "公里(仅供参考)")]), s("van-icon", { attrs: { name: "question-o" }, on: { click: function (i) { t.showCk = !0 } } }), t.orderData.states >= 3 ? s("span", { staticClass: "li-btn", on: { click: t.goPath } }, [t._v("查看详情")]) : t._e()], 1)]) : t._e(), t.orderData.packageImgsList && t.orderData.packageImgsList.length > 0 ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-name" }, [t._v("包裹图片")]), s("div", { staticClass: "li-value" }, [s("span", { staticClass: "li-btn", on: { click: t.lookImg } }, [t._v("查看")])])]) : t._e(), t.orderData.arrivalGoods ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-name" }, [t._v("是否到货")]), s("div", { staticClass: "li-value" }, [t.orderData.isArrivedAppointBusiness ? [t._v(" " + t._s(t.orderData.isArrive == 1 ? "已到货" : "可联系商家或平台询问到货情况") + " ")] : [t._v(" " + t._s(t.orderData.arrivalGoods == "未到货" ? t.orderData.arrivalGoods + "(预计".concat(t.orderData.planArrivalGoodsTime, "到货)") : t.orderData.arrivalGoods) + " ")]], 2)]) : t._e(), t.orderData.descr ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-name" }, [t._v("物流备注")]), s("div", { staticClass: "li-value", staticStyle: { "word-break": "normal", "word-wrap": "break-word" } }, [t._v(" " + t._s(t.orderData.descr) + " ")])]) : t._e(), t.orderData.sameDayServiceDate && t.orderData.isSameDay == 1 ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("到货情况")]), s("div", { staticClass: "li-txt rt drd", on: { click: t.openWl } }, [t.orderData.isArrivedAppointBusiness ? [t._v(" " + t._s(t.orderData.isArrive == 1 ? "已到货" : "可联系商家或平台询问到货情况") + " ")] : [t._v(" " + t._s(t.orderData.sameDayServiceDate) + " ")], s("van-icon", { staticStyle: { "vertical-align": "-2px" }, attrs: { name: "arrow" } })], 2)]) : t._e()])], 1) : s("div", { staticClass: "new-padd new-main-2021" }, [t.ischildren ? s("div", [t._m(0), s("van-divider"), s("div", { staticClass: "new-list flex-list", attrs: { id: "parent-element" } }, [(t.orderData.type == "安装" || t.orderData.servicetype == "lz") && (t.orderData.busserId != 1328 || t.orderData.isTmallTag == 7 || t.orderData.isTmallTag == 10) ? [t.orderData.servicetype == "lz" && !t.orderData.arrivalGoods ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("到货情况")]), s("div", { staticClass: "li-txt rt" }, [t._v("请主动询问到货情况")])]) : [t.orderData.arrivalGoods && t.orderData.isSameDay != 1 ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("到货情况")]), s("div", { staticClass: "li-txt rt" }, [t.orderData.isArrivedAppointBusiness ? [t._v(" " + t._s(t.orderData.isArrive == 1 ? "已到货" : "可联系商家或平台询问到货情况") + " ")] : [t._v(" " + t._s(t.orderData.arrivalGoods) + " ")]], 2)]) : t._e()], t.orderData.arrivalGoods && t.orderData.isSameDay != 1 ? s("div", { staticClass: "li-search" }, [t.orderData.states <= 5 && t.orderData.states >= 3 ? s("van-tag", { staticClass: "copy-btn", attrs: { plain: "", type: "primary" }, on: { click: function (i) { return t.queryLogistics(t.logistics[0]) } } }, [t._v("查询")]) : t._e()], 1) : t._e(), t.orderData.isPS != 1 ? [t.orderData.logisticsCompany ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("物流公司")]), s("div", { staticClass: "li-txt rt" }, [t._v(" " + t._s(t.orderData.logisticsCompany) + " ")])]) : t._e(), t.orderData.logisticsSingle ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("物流单号")]), s("div", { staticClass: "li-txt rt" }, [t._v(" " + t._s(t.orderData.logisticsSingle) + " ")])]) : t._e()] : t._e()] : t._e(), (t.orderData.type == "配送+安装" || t.orderData.servicetype == "cz") && t.orderData.busserId == 1328 ? [t.orderData.arrivalGoods && t.orderData.isSameDay != 1 ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("到货情况")]), s("div", { staticClass: "li-txt rt" }, [t.orderData.isArrivedAppointBusiness ? [t._v(" " + t._s(t.orderData.isArrive == 1 ? "已到货" : "可联系商家或平台询问到货情况") + " ")] : [t._v(" " + t._s(t.orderData.arrivalGoods) + " ")]], 2)]) : t._e(), t.orderData.arrivalGoods && t.orderData.isSameDay != 1 ? s("div", { staticClass: "li-search" }, [t.orderData.states <= 5 && t.orderData.states >= 3 ? s("van-tag", { staticClass: "copy-btn", attrs: { plain: "", type: "primary" }, on: { click: function (i) { return t.queryLogistics(t.logistics[0]) } } }, [t._v("查询")]) : t._e()], 1) : t._e(), t.orderData.isPS != 1 ? [t.orderData.logisticsCompany ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("物流公司")]), s("div", { staticClass: "li-txt rt" }, [t._v(" " + t._s(t.orderData.logisticsCompany) + " ")])]) : t._e(), t.orderData.logisticsSingle ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("物流单号")]), s("div", { staticClass: "li-txt rt" }, [t._v(" " + t._s(t.orderData.logisticsSingle) + " ")])]) : t._e()] : t._e()] : t._e(), [t.orderData.servicetype == "wx" && t.orderData.logisticsSingle ? s("div", [s("div", { staticClass: "list-li", staticStyle: { "margin-bottom": "-0.1rem" } }, [s("div", { staticClass: "li-title lf" }, [t._v("物流公司")]), s("div", { staticClass: "li-txt rt" }, [t._v(" " + t._s(t.orderData.logisticsCompany) + " ")])]), s("div", { staticClass: "list-li", staticStyle: { "justify-content": "space-between" } }, [s("div", { staticClass: "li-title lf" }, [t._v("物流单号")]), s("div", { staticClass: "li-txt rt", staticStyle: { display: "block", "text-align": "right" } }, t._l(t.logistics, function (i) { return t.logistics && t.logistics.length < 2 ? s("div", { staticStyle: { margin: "0.15rem 0" } }, [s("span", { attrs: { id: "copy_texts" } }, [t._v(t._s(i))]), t.orderData.states <= 5 && t.orderData.states >= 3 ? s("van-tag", { staticClass: "copy-btn", attrs: { plain: "", type: "primary" }, on: { click: function (a) { return t.queryLogistics(i) } } }, [t._v("查询")]) : t._e()], 1) : t._e() }), 0)]), t.logistics && t.logistics.length > 1 ? s("div", [t._l(t.logistics, function (i) { return s("div", { staticStyle: { float: "left", margin: "0.15rem 0", "margin-right": "0.4rem" } }, [s("span", { attrs: { id: "copy_texts" } }, [t._v(t._s(i))]), t.orderData.states <= 5 && t.orderData.states >= 3 ? s("van-tag", { staticClass: "copy-btn", attrs: { plain: "", type: "primary" }, on: { click: function (a) { return t.queryLogistics(i) } } }, [t._v("查询")]) : t._e()], 1) }), s("div", { staticStyle: { clear: "both" } })], 2) : t._e()]) : t._e()], t.orderData.sameDayServiceDate && t.orderData.isSameDay == 1 ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("到货情况")]), s("div", { staticClass: "li-txt rt drd", on: { click: t.openWl } }, [t.orderData.isArrivedAppointBusiness ? [t._v(" " + t._s(t.orderData.isArrive == 1 ? "已到货" : "可联系商家或平台询问到货情况") + " ")] : [t._v(" " + t._s(t.orderData.sameDayServiceDate) + " "), s("van-icon", { staticStyle: { "vertical-align": "-2px" }, attrs: { name: "arrow" } })]], 2)]) : t._e()], 2)], 1) : t._e()]), s("van-image-preview", { attrs: { closeable: !0, images: t.images }, scopedSlots: t._u([{ key: "index", fn: function () { return [t._v("第{ index }页")] }, proxy: !0 }]), model: { value: t.imgBg, callback: function (i) { t.imgBg = i }, expression: "imgBg" } }), s("van-dialog", { attrs: { title: "温馨提示", message: "配送距离为物流点至客户地址距离,仅供参考", "confirm-button-text": "我知道了", "confirm-button-color": "#FE4932" }, on: { confirm: function (i) { t.showCk = !1 } }, model: { value: t.showCk, callback: function (i) { t.showCk = i }, expression: "showCk" } }), s("tile-button-dialog", { attrs: { title: "物流信息", "confirm-button-text": "我知道了" }, on: { confirm: function (i) { t.showWl = !1 } }, model: { value: t.showDr, callback: function (i) { t.showDr = i }, expression: "showDr" } }, [t.logisticsInfo ? s("div", { staticClass: "wl-main" }, [s("div", { staticClass: "wl-list" }, [s("van-steps", { attrs: { direction: "vertical", active: 0, "active-color": "#FE4932" } }, t._l(t.logisticsInfo, function (i, a) { return s("van-step", { key: a }, [s("h3", [t._v(t._s(i.remark))]), s("p", [t._v(t._s(i.createTime))])]) }), 1)], 1)]) : s("div", { staticClass: "wl-none-wrap" }, [s("img", { staticClass: "wl-none-img", attrs: { src: "https://osscdn.lbdj.com/J/G/G-A/devCustom/20240618/common/car-empty.png" } }), s("div", { staticClass: "wl-none-text" }, [t._v("暂无物流")])])]), s("van-dialog", { attrs: { width: "6rem", "confirm-button-text": "我知道了", "confirm-button-color": "#F33434" }, model: { value: t.isShowAddressTipDialog, callback: function (i) { t.isShowAddressTipDialog = i }, expression: "isShowAddressTipDialog" } }, [s("div", { staticClass: "address-tip" }, [t._v(t._s(t.orderData.popPickupAddress))])])], 1) }, M = [function () { var e = this, t = e._self._c; return t("div", { staticClass: "new-title" }, [t("div", { staticClass: "new-title-left" }, [e._v("物流详情")])]) }], V = n(B, q, M, !1, null, "109ba865", null, null); const js = V.exports; const U = { name: "Dialog", data() { return { title: "这是一个测试的数据标题", show: !0 } }, created() { }, methods: { closeWin() { const e = { state: !1, type: "jd" }; this.$emit("closeWin", e) } } }; var z = function () { var t = this, s = t._self._c; return s("div", { staticClass: "dialog" }, [s("van-dialog", { attrs: { title: "京东订单好评返现怎么获得？", "show-confirm-button": !1 }, model: { value: t.show, callback: function (i) { t.show = i }, expression: "show" } }, [s("div", { staticClass: "show-main" }, [s("div", { staticClass: "show-p" }, [s("p", [t._v(" 京东订单好评返现需要师傅安装完成之后引导客户在京东APP订单上进行产品的五星评价后，页面有个单独的安装服务可以进行五星勾选，需要客户满五星评价订单才算师傅好评成功。 ")]), s("p", [t._v("如果客户订单是在淘宝APP则没有安装服务评价入口，订单则没有好评返现。")])])]), s("div", { staticClass: "win-btn btn-pro" }, [s("button", { on: { click: t.closeWin } }, [t._v("我知道了")])])])], 1) }, W = [], H = n(U, z, W, !1, null, "4ab742e9", null, null); const C = H.exports; const Q = { name: "Dialog", data() { return { title: "这是一个测试的数据标题", show: !0 } }, created() { }, methods: { closeWin() { const e = { state: !1, type: "goods" }; this.$emit("closeWin", e) } } }; var J = function () { var t = this, s = t._self._c; return s("div", { staticClass: "dialog" }, [s("van-dialog", { attrs: { title: "好评返现如何获得？", "show-confirm-button": !1 }, model: { value: t.show, callback: function (i) { t.show = i }, expression: "show" } }, [s("div", { staticClass: "show-main" }, [s("div", { staticClass: "show-p" }, [s("p", [t._v("什么是好评返现？")]), s("p", [t._v("好评返现是指师傅可以通过引导客户给予产品卖家好评来获取现金返现奖励。")])]), s("div", { staticClass: "show-p" }, [s("p", [t._v("如何获取好评返现？")]), s("p", [t._v(" 师傅服务完成后，现场引导客户前往产品购买店铺给予好评，客户完成好评操作后，师傅拍下客户手机好评界面照片，并将照片上传至【确认完成】好评照片处。 ")])]), s("div", { staticClass: "show-p" }, [s("p", [t._v("如何判断返现是否获得？")]), s("p", [t._v("师傅提交好评照片后，审核通过或不通过都有通知提醒，通知包括软件内消息和短信提醒。")])]), s("div", { staticClass: "show-p", staticStyle: { color: "red" } }, [s("p", [t._v("平台警告：")]), s("p", [t._v("客户好评仅限于引导！不得强迫、威胁、恐吓客户给予好评，一经发现严厉处罚！")])])]), s("div", { staticClass: "win-btn btn-pro" }, [s("button", { on: { click: t.closeWin } }, [t._v("我知道了")])])])], 1) }, K = [], Y = n(Q, J, K, !1, null, "e39bbec8", null, null); const w = Y.exports; const X = { props: { value: { type: Boolean, default: !1 }, orderData: { type: Object, default: () => ({}) } }, components: {}, data() { return { checkFee: "" } }, computed: { popupShow: { get() { return this.value }, set(e) { this.$emit("input", e) } } }, created() { this.getFee() }, mounted() { }, methods: { async getFee() { const e = await k({ orderId: this.orderData.id }); e.code == 200 && (this.checkFee = e.data.checkFee || "") }, closeClick() { this.$emit("input", !1) } } }; var Z = function () { var t = this, s = t._self._c; return s("div", [s("van-popup", { attrs: { "close-on-click-overlay": !1 }, model: { value: t.popupShow, callback: function (i) { t.popupShow = i }, expression: "popupShow" } }, [s("div", { staticClass: "popup" }, [s("div", { staticClass: "title" }, [t._v("费用复核说明")]), s("div", { staticClass: "fee" }, [t._v(" 增加费用 ： "), s("span", { staticClass: "num" }, [t._v(t._s(t.checkFee) + "元")])]), s("div", { staticClass: "content" }, [t._v(" 平台正在对该笔费用进行复核，请确保改装前后的 图片拍摄和上传符合图例要求，图片不规范有可能 导致费用复核失败! ")]), s("div", { staticClass: "btn", on: { click: t.closeClick } }, [t._v("我知道了")])])])], 1) }, tt = [], st = n(X, Z, tt, !1, null, "f9e338b2", null, null); const y = st.exports; const et = { name: "Hello", props: ["orderData", "conceal", "type", "orderId"], components: { jingdongShow: C, goodsShow: w, recheckFeeDialog: y }, data() { return { imgUrl: "https://osscdn.lbdj.com/", title: "这是一个测试的数据标题", isMt: !1, materialsData: null, isShow: !1, latelList: [], labelShow: !1, labelTxt: "", labelName: "", goodsShow: !1, jingdongShow: !1, priceShow: !1, balanceExplainShow: !1, recheckFeShow: !1 } }, filters: { formatNumber(e) { return typeof e == "number" ? e.toFixed(2) : e } }, created() { }, methods: { recheckClick() { this.recheckFeShow = !0 }, selfBiddingLabelBtn() { this.$toast("该商品服务费用为商家自主出价，师傅可基于实际情况考虑是否接单") }, concealOr() { let e; this.conceal ? e = !1 : e = !0, this.orderData.displayOrderFee == 0 && !this.conceal ? this.priceShow = !0 : this.$emit("showConceal", e) }, openGoodsShow() { this.orderData.isTmallTag == 3 ? this.jingdongShow = !0 : this.goodsShow = !0 }, closeGoodsShow(e) { e.type == "jd" ? this.jingdongShow = e.state : e.type == "goods" && (this.goodsShow = e.state) }, handleCount(e) { return e ? (e = parseFloat(e), e.toFixed(2)) : e || 0 }, goRates() { this.$router.push({ path: "/order/rates", query: { materialsData: JSON.stringify(this.materialsData) } }) }, assistMaterials() { const e = this, t = { firstCategoryId: this.orderData.cid, source: this.orderData.orderChannel, orderId: this.orderId }; h(t).then(s => { s.code == 200 ? this.materialsData = s.data : e.$toast(s.msg) }, function (s) { }) }, showLabel(e, t) { t && (this.labelShow = !0, this.labelName = e, this.labelTxt = t) }, formattingLatel() { var s; const e = [], t = this.orderData; t.isOfflineOrder == 1 && e.push({ title: "门店", center: "来自线下门店/企业发布的订单" }), t.urgentMoney && e.push({ title: "加急", center: "请在接单后当天内完成任务则可获得加急费" }), (t.orderChannel == 1 && !t.busofferno || t.cid == "130" || t.freeCommonsionFlag == 1) && e.push({ title: "不抽佣", center: "完工后可获得100%订单费用" }), t.ordertype == 6 && e.push({ title: "自费", center: "先支付费用，再向客户索取" }), t.ordertype == 4 && e.push({ title: "半自费", center: "上门服务后，再向客户收取部分费用" }), (t.adjustprice == 1 || ((s = t.speedOrder) == null ? void 0 : s.ptSpeedPrice) == 1) && e.push({ title: "加价", center: "商家已经增加费用的订单" }), t.isCirculationOrder == 1 && e.push({ title: "不占次数", center: "带有 不占抢单次数 的订单，不占用你每日抢单的次数，且当天抢单次数用完的情况下也可以接此类订单" }), (t.isSecondClock == 1 || t.thirdservicetype == "serviceAgain") && e.push({ title: "二次上门", center: "需二次上门进行服务" }), t.detection == 1 && e.push({ title: "检修单", center: "上门检测影响产品正常使用的问题点，若不需要更换配件，师傅需帮助调试产品，达到正常使用即可" }), t.debugOrder == 1 && e.push({ title: "调试单", center: "" }), t.offlineQuaSubsidyLabel && e.push({ title: t.offlineQuaSubsidyLabel, center: t.offlineQuaSubsidyPopStr }), t.elevenTag == 1 && e.push({ title: "双11补贴", center: "此订单支持组队补贴活动、完工抽奖活动" }), this.latelList = e } } }; var at = function () { var i, a, o, c; var t = this, s = t._self._c; return s("div", { staticClass: "main" }, [s("div", { staticClass: "new-padd new-main-2021" }, [s("div", { staticClass: "new-title" }, [s("div", { staticClass: "new-title-left" }, [t._v(" 订单金额 "), t.orderData.states >= 3 && t.orderData.amount ? s("van-tag", { staticClass: "copy-btn concealOr", attrs: { plain: "", type: "primary" }, on: { click: t.concealOr } }, [t.conceal ? s("img", { staticClass: "img1", attrs: { src: t.imgUrl + "lbdj_app_h5/page/xianshi2.png" } }) : t._e(), t.conceal ? t._e() : s("img", { staticClass: "img2", attrs: { src: t.imgUrl + "lbdj_app_h5/page/xianshi1.png" } })]) : t._e()], 1)]), s("van-divider"), t.type == 1 ? s("div", { staticClass: "new-list" }, [t._m(0)]) : s("div", { staticClass: "new-list" }, [t.type == 1 ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("订单服务费")]), t._m(1), s("div", { staticClass: "co" })]) : t._e(), t.orderData.type == "配送+安装" || t.orderData.servicetype == "cz" || t.orderData.servicetype == "ps" ? [t.orderData.baseMoneyStr && t.orderData.baseMoneyStr != "0.00" ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("订单服务费")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.baseMoneyStr))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.ordeBaseFee ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("订单基础商品费")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.ordeBaseFeeStr))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.exceptionFee && Number(t.orderData.exceptionFee) ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("异常完工费")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.exceptionFee))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.infoFee && Number(t.orderData.infoFee) ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("信息服务费用")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.infoFee.toFixed(2)))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.emptyRunningFee ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("空跑费")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.emptyRunningFee.toFixed(2)))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t._l(t.orderData.orderAttch, function (r, d) { return r.attchFee ? s("div", { key: d, staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v(t._s(r.attchFeeName))]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(r.attchFee))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e() }), t.orderData.urgentMoney ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("加急费")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.urgentMoneyStr))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.makeGoodMoney ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v(" 好评奖励 ")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.makeGoodMoney))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")]), t.orderData.states >= 6 ? s("span", [t._v(" (" + t._s(t.orderData.makeGoodStatus == 2 ? "已完成" : t.orderData.makeGoodStatus == 3 ? "未通过" : "未完成") + ") ")]) : t._e()]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.orderaddfee ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v(" 增加费用 "), t.orderData.checkFeeExistFlag == 1 ? s("div", { staticClass: "recheck", on: { click: function (r) { return t.recheckClick() } } }, [t._v(" 费用复核中 ")]) : t._e()]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.orderaddfeeStr))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.addFee > 0 && t.orderData.states < 3 ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v(" 增加费用 "), t.orderData.checkFeeExistFlag == 1 ? s("div", { staticClass: "recheck", on: { click: function (r) { return t.recheckClick() } } }, [t._v(" 费用复核中 ")]) : t._e()]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.addFee))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), (i = t.orderData.speedOrder) != null && i.orderSpeedFee ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("平台补贴")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s((a = t.orderData.speedOrder) == null ? void 0 : a.orderSpeedFee))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.refundAmount ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("订单退款")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.refundAmount))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.serviceWybFree ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("服务无忧")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.serviceWybFree.toFixed(2)))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e()] : [t.orderData.ordeBaseFee ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("订单基础商品费")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.ordeBaseFeeStr))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.exceptionFee && Number(t.orderData.exceptionFee) ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("异常完工费")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.exceptionFee))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.baseMoney && t.orderData.states < 3 && !t.orderData.ordeBaseFee ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("订单服务费")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.baseMoneyStr))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.orderServiceFee ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("订单服务费")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.orderServiceFee))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.highfillFee ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("高额补贴")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.highfillFee))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t._l(t.orderData.orderAttch, function (r, d) { return r.attchFee ? s("div", { key: d, staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v(t._s(r.attchFeeName))]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(r.attchFee))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e() }), t.orderData.orderaddfee ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v(" 增加费用 "), t.orderData.checkFeeExistFlag == 1 ? s("div", { staticClass: "recheck", on: { click: function (r) { return t.recheckClick() } } }, [t._v(" 费用复核中 ")]) : t._e()]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.orderaddfeeStr))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.customerPay ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("客户结算")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.customerPayStr))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.customerPayment ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("客户结算")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.customerPaymentStr))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.orderLogisticsFee && t.orderData.states >= 3 ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("物流运费")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.orderLogisticsFeeStr))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.infoFee && Number(t.orderData.infoFee) ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf serve" }, [t._v(" 信息服务费用 ")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.infoFee.toFixed(2)))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.emptyRunningFee ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("空跑费")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.emptyRunningFee.toFixed(2)))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.urgentMoney ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("加急费")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.urgentMoneyStr))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.makeGoodMoney ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v(" 好评奖励 ")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.makeGoodMoneyStr))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")]), t.orderData.states > 6 ? s("span", [t._v(" (" + t._s(t.orderData.makeGoodStatus == 2 ? "已完成" : t.orderData.makeGoodStatus == 3 ? "未通过" : "未完成") + ") ")]) : t._e()]), s("div", { staticClass: "co" })]) : t._e(), (o = t.orderData.speedOrder) != null && o.orderSpeedFee ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("平台补贴")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s((c = t.orderData.speedOrder) == null ? void 0 : c.orderSpeedFee))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.refundAmount ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("订单退款")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.refundAmount))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.serviceWybFree ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("服务无忧")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.serviceWybFree.toFixed(2)))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.adjustFee ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("加价金额")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.adjustFee))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.addFee > 0 && t.orderData.states < 3 ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v(" 增加费用 "), t.orderData.checkFeeExistFlag == 1 ? s("div", { staticClass: "recheck", on: { click: function (r) { return t.recheckClick() } } }, [t._v(" 费用复核中 ")]) : t._e()]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.addFee))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e()], t.orderData.psFee ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("配送费")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.psFee))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.carryFee ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("搬运费")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.carryFee))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.bargainDiffFee ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("议价成功后的差价")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.handleCount(t.orderData.bargainDiffFee)))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.returnStoreFee ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf lf-i" }, [t._v(" 配件返厂费 "), s("van-icon", { attrs: { name: "question-o" }, on: { click: function (r) { t.isShow = !0 } } })], 1), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.returnStoreFee))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.returnStoreExpressFee ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("配件返厂快递费")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.returnStoreExpressFee))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.sameDayMoney ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("当日达费用")]), s("div", { staticClass: "li-txt rt" }, [t.conceal ? s("span", [t._v("￥" + t._s(t.orderData.sameDayMoney))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.selfFeeAmount ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("业主自费金额")]), s("div", { staticClass: "li-txt rt" }, [s("span", [t._v("￥" + t._s(t._f("formatNumber")(t.orderData.selfFeeAmount)))])]), s("div", { staticClass: "co" })]) : t._e(), s("div", { staticClass: "cost-bottom cost-bottom-new" }, [s("div", [t.orderData.selfBiddingLabel ? s("div", { staticClass: "selfBiddingLabel-box", on: { click: t.selfBiddingLabelBtn } }, [t._v(" 商家自主出价 ")]) : t._e()]), s("div", [t._v(" 总计: "), t.conceal ? s("span", { staticStyle: { "font-weight": "bold", color: "#f33434" } }, [t._v("￥")]) : t._e(), t.conceal ? s("span", { staticClass: "b" }, [t._v(t._s(t.orderData.totalFee))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])])])], 2), s("van-dialog", { attrs: { title: "配件返厂费", message: "指用户下单选择配件返厂需支付师傅的服务费，订单完结后此费用将结算至账户钱包", "confirm-button-text": "我知道了", "confirm-button-color": "#FE4932" }, on: { confirm: function (r) { t.isShow = !1 } }, model: { value: t.isShow, callback: function (r) { t.isShow = r }, expression: "isShow" } }), s("van-dialog", { attrs: { title: t.labelName, message: t.labelTxt, "confirm-button-color": "#FE4932", "confirm-button-text": "我知道了" }, on: { confirm: function (r) { t.labelShow = !1 } }, model: { value: t.labelShow, callback: function (r) { t.labelShow = r }, expression: "labelShow" } }), s("div", { staticClass: "price-box" }, [s("van-popup", { attrs: { closeable: "", round: "" }, model: { value: t.priceShow, callback: function (r) { t.priceShow = r }, expression: "priceShow" } }, [s("div", { staticClass: "price-show" }, [s("div", { staticClass: "title" }, [t._v("温馨提示")]), s("div", { staticClass: "msg" }, [t._v("服务价格为敏感信息，切勿透露给客户，否则师傅您将可能面临商家投诉")]), s("div", { staticClass: "btn", on: { click: function (r) { t.$emit("showConceal", !0), t.priceShow = !1 } } }, [t._v("我知道了")])])])], 1), t.goodsShow ? s("goods-show", { on: { closeWin: t.closeGoodsShow } }) : t._e(), t.jingdongShow ? s("jingdong-show", { on: { closeWin: t.closeGoodsShow } }) : t._e(), s("van-popup", { staticClass: "costPopup", attrs: { round: "" }, on: { close: function (r) { t.isWin = !1 } }, model: { value: t.balanceExplainShow, callback: function (r) { t.balanceExplainShow = r }, expression: "balanceExplainShow" } }, [s("div", [s("div", { staticClass: "head" }, [t._v("增加费用/信息服务费用说明")]), s("div", { staticClass: "content" }, [s("div", [t._v("计算公式")]), s("div", [t._v("信息服务费=增加费用金额*10%")])]), s("div", { staticClass: "tips" }, [t._v("根据平台规则，增加费用收取10%的信息服务费")]), s("div", { staticClass: "btn", on: { click: function (r) { t.balanceExplainShow = !1 } } }, [t._v("我知道了")])])]), t.recheckFeShow ? s("recheckFeeDialog", { attrs: { orderData: t.orderData }, model: { value: t.recheckFeShow, callback: function (r) { t.recheckFeShow = r }, expression: "recheckFeShow" } }) : t._e()], 1)]) }, it = [function () { var e = this, t = e._self._c; return t("div", { staticClass: "list-li" }, [t("div", { staticClass: "li-title lf" }, [e._v("订单服务费")]), t("div", { staticClass: "li-txt rt" }, [t("span", { staticStyle: { "font-size": "15px" } }, [e._v("如有意向请提交你的报价")])]), t("div", { staticClass: "co" })]) }, function () { var e = this, t = e._self._c; return t("div", { staticClass: "li-txt rt" }, [t("span", { staticStyle: { "font-size": "15px" } }, [e._v("如有意向请提交你的报价")])]) }], ot = n(et, at, it, !1, null, "7c979309", null, null); const Rs = ot.exports; const rt = { props: { value: { type: Boolean, default: !1 } }, components: {}, data() { return { imgAge: "https://osscdn.lbdj.com/worker-mini-program/h5/", descriptionList: [{ label: "订单总计金额计算公式", value: "订单金额总计=基础工钱+额外奖励-订单退款-服务无忧" }, { label: "基础工钱", value: "基础工钱=基础服务费+配件返厂费+代付运费+配件返厂快递费+增加费用+平台补贴+高空作业费+附加费", look: !0, seleted: !0, subList: [{ title: "基础服务费", text: "基础服务费为师傅的到手工钱，订单完工后可获得100%的费用！" }, { title: "配件返厂费", text: "部分订单商家会支付一笔配件返厂费" }, { title: "代付运费", text: "部分订单商家会支付一笔代付运费" }, { title: "配件返厂快递费", text: "部分订单需要支付师傅垫付配件寄回的费用" }, { title: "增加费用", text: "在服务过程中根据实际情况，由商家发起或由师傅申请发起的增加费用（包括空跑费）" }, { title: "平台补贴", text: "师傅只要接单即可获得一笔接单感谢费" }] }, { label: "额外奖励", value: "额外奖励=平台奖励+加急费+好评奖励+平台高补+订单加价", look: !0, seleted: !0, subList: [{ title: "平台奖励", text: "不同的订单可根据师傅保证金和星级等级获得比例不等的平台奖励" }, { title: "加急费", text: "请在接单后当天内完成任务则可获得加急费" }, { title: "好评奖励", text: "好评奖励是指师傅可以通过引导客户给予产品卖家好评来获取现金奖励" }, { title: "平台高补", text: "师傅接单即可获得订单的平台高额补贴" }], careful: !0 }, { label: "订单退款", value: "在服务过程中商家发起订单退款，师傅同意后，订单退款将从订单的总计费用中扣减", look: !0, seleted: !1, subList: [] }, { label: "服务无忧", value: "每笔订单费用结算都会扣除一定的服务无忧费，具体金额以实际接单为准", look: !0, seleted: !1, subList: [] }] } }, computed: { show: { get() { return this.value }, set(e) { this.$emit("input", e) } } }, created() { }, mounted() { }, methods: {} }; var lt = function () { var t = this, s = t._self._c; return s("div", [s("van-popup", { attrs: { position: "bottom", round: "" }, model: { value: t.show, callback: function (i) { t.show = i }, expression: "show" } }, [s("div", { staticClass: "popup" }, [s("div", { staticClass: "head" }, [t._v("订单费用说明")]), s("div", { staticClass: "content" }, t._l(t.descriptionList, function (i, a) { return s("div", { key: a, staticClass: "cell" }, [s("div", { staticClass: "label" }, [t._v(" " + t._s(i.label) + " "), i.seleted ? [i.look ? s("img", { staticClass: "arrow", attrs: { src: "".concat(t.imgAge, "img20.png"), alt: "" }, on: { click: function (o) { i.look = !i.look } } }) : t._e(), i.look ? t._e() : s("img", { staticClass: "arrow", attrs: { src: "".concat(t.imgAge, "img19.png"), alt: "" }, on: { click: function (o) { i.look = !i.look } } })] : t._e()], 2), s("div", { staticClass: "value" }, [t._v(t._s(i.value))]), !i.look && i.seleted ? s("div", { staticClass: "Introduction" }, [t._l(i.subList, function (o, c) { return s("div", { key: c, staticClass: "sub-cell" }, [s("div", { staticClass: "title" }, [t._v(t._s(o.title))]), s("div", { staticClass: "text" }, [t._v(t._s(o.text))])]) }), i.careful ? s("div", { staticClass: "careful" }, [t._v("注：额外奖励以订单实际是否包含奖励项目为准")]) : t._e()], 2) : t._e()]) }), 0), s("div", { staticClass: "bottom", on: { click: function (i) { return t.$emit("input", !1) } } }, [t._v("我知道了")])])])], 1) }, nt = [], ct = n(rt, lt, nt, !1, null, "ed1dab74", null, null); const dt = ct.exports; const vt = { model: { prop: "isShow", event: "close" }, props: { isShow: { type: Boolean, default: !1 }, money: { type: String, default: "" } }, data() { return {} }, methods: { closePopup() { this.$emit("close", !1) } } }; var _t = function () { var t = this, s = t._self._c; return t.isShow ? s("div", { staticClass: "chunjie-popup", on: { touchmove: function (i) { i.stopPropagation(), i.preventDefault() } } }, [s("div", { staticClass: "popup-content" }, [s("div", { staticClass: "content" }, [s("span", [t._v("春节不打烊～接单即可获得平台额外补贴"), s("span", { staticClass: "money" }, [t._v(t._s(t.money || ""))]), t._v("元，该笔费用为师傅你的接单感谢费！")])]), s("div", { staticClass: "btn", on: { click: t.closePopup } }, [t._v("我知道了")])])]) : t._e() }, pt = [], ht = n(vt, _t, pt, !1, null, "cd406424", null, null); const ut = ht.exports; const ft = { name: "CostInfoNew", props: { orderData: { type: Object, default: () => ({}) }, conceal: { type: Boolean, default: !0 }, showChunjieAd: { type: Boolean, default: !0 }, imgAge: { type: String, default: "" } }, data() { return {} }, methods: {} }; var gt = function () { var t = this, s = t._self._c; return s("div", { staticClass: "cost-items" }, t._l(t.orderData.newNotTaskFeeList, function (i, a) { return s("div", { key: a, staticClass: "price-list" }, [s("div", { staticClass: "total" }, [s("span", [t._v(" " + t._s(i.feeName) + " "), i.feeName == "额外奖励" ? s("img", { staticClass: "red-packet", attrs: { src: "".concat(t.imgAge, "img10.png"), alt: "" } }) : t._e()]), t.conceal ? s("span", { staticStyle: { color: "#3a3a3c" } }, [t._v(" ¥" + t._s(i.feeStr))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), i.list && i.list.length ? t._l(i.list, function (o, c) { return s("div", { key: c, staticClass: "flex-box" }, [s("div", { staticClass: "cell" }, [s("span", { staticClass: "left" }, [t._v(" " + t._s(o.feeName) + " "), t.showChunjieAd && o.feeName == "平台补贴" ? s("img", { staticClass: "chunjie-icon", attrs: { src: "https://osscdn.lbdj.com/J/G/G-A/devCustom/20240108/chunjie/pic2.png", alt: "" }, on: { click: function (r) { return t.$emit("show-chunjie") } } }) : t._e(), o.feeName == "信息服务费" ? s("img", { staticClass: "query-icon", attrs: { src: "https://osscdn.lbdj.com/worker-mini-program/mine/wallet/img39.png", alt: "" }, on: { click: function (r) { return t.$emit("show-info-explain") } } }) : t._e(), o.feeName == "增加费用" && t.orderData.checkFeeExistFlag == 1 ? s("div", { staticClass: "recheck", on: { click: function (r) { return t.$emit("show-fee-check") } } }, [t._v(" 费用复核中 ")]) : t._e()]), s("span", [o.mark && o.mark.indexOf("不抽佣") == -1 ? s("span", { staticClass: "failure", on: { click: function (r) { return r.stopPropagation(), t.$emit("show-failure", o) } } }, [t._v(" 已失效 "), s("img", { staticClass: "arrow", attrs: { src: "".concat(t.imgAge, "img26.png"), alt: "" } })]) : t._e(), t.conceal ? s("span", [t._v("¥" + t._s(o.feeStr))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])])]), o.feeName == "基础服务费" && o.list ? s("span", { staticClass: "lz-fee" }, t._l(o.list, function (r, d) { return s("span", { key: d, staticClass: "fee-item" }, [s("span", { staticClass: "feeName" }, [t._v(t._s(r.feeName))]), s("span", { staticClass: "feeStr" }, [t._v("¥" + t._s(r.feeStr))])]) }), 0) : t._e()]) }) : t._e()], 2) }), 0) }, mt = [], Ct = n(ft, gt, mt, !1, null, "16bf866a", null, null); const wt = Ct.exports; const yt = { name: "CostInfoNewBillTag", props: { orderData: { type: Object, default: () => ({}) }, conceal: { type: Boolean, default: !0 }, showChunjieAd: { type: Boolean, default: !0 }, imgAge: { type: String, default: "" } }, computed: { moneyData() { const { returnWorkerFeeDetail: e } = this.orderData; return e ? e.workerTypeFeeDetails : [] } } }; var Dt = function () { var t = this, s = t._self._c; return s("div", { staticClass: "cost-items" }, t._l(t.moneyData, function (i, a) { return s("div", { key: a, staticClass: "price-list" }, [s("div", { staticClass: "total" }, [s("span", [t._v(" " + t._s(i.feeName) + " "), i.feeType == 2 ? s("img", { staticClass: "red-packet", attrs: { src: "".concat(t.imgAge, "img10.png"), alt: "" } }) : t._e()]), t.conceal ? s("span", { staticStyle: { color: "#3a3a3c" } }, [t._v(" " + t._s(i.feeStr))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")])]), i.childDetails && i.childDetails.length ? t._l(i.childDetails, function (o, c) { return s("div", { key: c, staticClass: "flex-box" }, [s("div", { staticClass: "cell" }, [s("span", { staticClass: "left" }, [t._v(" " + t._s(o.feeName) + " "), t.showChunjieAd && o.feeType == 109 ? s("img", { staticClass: "chunjie-icon", attrs: { src: "https://osscdn.lbdj.com/J/G/G-A/devCustom/20240108/chunjie/pic2.png", alt: "" }, on: { click: function (r) { return t.$emit("show-chunjie") } } }) : t._e(), o.feeType == 105 && t.orderData.checkFeeExistFlag == 1 ? s("div", { staticClass: "recheck", on: { click: function (r) { return t.$emit("show-fee-check") } } }, [t._v(" 费用复核中 ")]) : t._e()]), s("span", [o.markType && o.markType == 2 ? s("span", { staticClass: "failure", on: { click: function (r) { return r.stopPropagation(), t.$emit("show-failure", o) } } }, [t._v(" 已失效 "), s("img", { staticClass: "arrow", attrs: { src: "".concat(t.imgAge, "img26.png"), alt: "" } })]) : t._e(), t.conceal ? s("span", { staticClass: "right" }, [t._v(t._s(o.feeStr))]) : t._e(), t.conceal ? t._e() : s("span", { staticClass: "right" }, [t._v("******")])])]), o.feeType == 101 && o.childDetails ? s("span", { staticClass: "lz-fee" }, t._l(o.childDetails, function (r, d) { return s("span", { key: d, staticClass: "fee-item" }, [s("span", { staticClass: "feeName" }, [t._v(t._s(r.feeName))]), s("span", { staticClass: "feeStr" }, [t._v(t._s(r.feeStr))])]) }), 0) : t._e()]) }) : t._e()], 2) }), 0) }, bt = [], kt = n(yt, Dt, bt, !1, null, "5fa1065e", null, null); const xt = kt.exports; const St = { props: { title: { type: String, default: "" }, content: { type: String, default: "" }, type: { type: "normal" | "simple", default: "normal" }, simpleBtnText: { type: String, default: "我知道了" }, cancelBtnText: { type: String, default: "取消" }, confirmBtnText: { type: String, default: "确定" } }, data() { return { labelShow: !0 } }, mounted() { }, methods: { close() { this.$emit("close") }, cancel() { this.close() }, confirm() { this.$emit("confirm"), this.close() } } }; var It = function () { var t = this, s = t._self._c; return s("div", { staticClass: "order-common-confirm-dialog-wrap" }, [s("van-dialog", { staticClass: "label-confirm-dialog", attrs: { "show-confirm-button": !1, "show-cancel-button": !1 }, scopedSlots: t._u([{ key: "title", fn: function () { return [s("span", { staticClass: "label-name" }, [t._v(t._s(t.title))])] }, proxy: !0 }]), model: { value: t.labelShow, callback: function (i) { t.labelShow = i }, expression: "labelShow" } }, [s("div", { staticClass: "content-wrap" }, [s("div", { staticClass: "label-text" }, [t._t("default", function () { return [t._v(t._s(t.content))] })], 2), t.type === "simple" ? s("div", { staticClass: "simple-btn-text", on: { click: t.cancel } }, [t._v(" " + t._s(t.simpleBtnText) + " ")]) : s("div", { staticClass: "btn-wrap" }, [s("div", { staticClass: "cancel", on: { click: t.cancel } }, [t._v(t._s(t.cancelBtnText))]), s("div", { staticClass: "confirm", on: { click: t.confirm } }, [t._v(t._s(t.confirmBtnText))])])])])], 1) }, $t = [], Ft = n(St, It, $t, !1, null, "a738a666", null, null); const D = Ft.exports; const Tt = { name: "CostInfoNew", props: ["orderData", "conceal", "type", "orderId"], components: { confirmDialog: D, costDescription: dt, chunjieSubsidy: ut, recheckFeeDialog: y, NewVersion: wt, NewBillTagVersion: xt }, data() { return { imgUrl: "https://osscdn.lbdj.com/", title: "这是一个测试的数据标题", isMt: !1, materialsData: null, latelList: [], labelShow: !1, labelTxt: "", labelName: "", goodsShow: !1, jingdongShow: !1, priceShow: !1, imgAge: "https://osscdn.lbdj.com/worker-mini-program/h5/", costShow: !1, failureShow: !1, showChunjieSubsidy: !1, chunjieMoney: "", failureReason: {}, showChunjieAd: !1, balanceExplainShow: !1, recheckFeShow: !1 } }, created() { this.formattingLatel(), this.toGetChunjieFee(), this.getSpringInfo() }, computed: { totalFee() { const { orderData: e } = this; return e.isNewBillTag ? e.totalFee : e.newNotTaskTotalFeeStr }, feeComponent() { const { orderData: e } = this; return e.isNewBillTag ? "NewBillTagVersion" : "NewVersion" }, isShowTotalFee() { const { isNewBillTag: e, totalFee: t } = this.orderData; return e ? t !== null : !0 } }, methods: { closePriceShowDialog() { this.$emit("showConceal", !0), this.priceShow = !1 }, getSpringInfo() { P({}).then(e => { e.code == 200 && e.data ? this.showChunjieAd = e.data.result == 1 : this.showChunjieAd = !1 }) }, failureClick(e) { if (this.failureReason = e, e.feeName == "平台奖励") { const t = { 售后: "订单在服务过程中产生投诉售后且成立，平台奖励已失效", 投诉: "订单在服务过程中产生投诉售后且成立，平台奖励已失效", 部分退款: "订单在服务过程中产生订单退款，平台奖励已失效" }[e.mark]; this.failureReason.content = t || "订单在服务过程中，因更换师傅/指派增加/交易关闭等某种原因，平台奖励已失效" } else this.failureReason.content = { 好评奖励: "1、未邀请客户给商家好评，未获得好评奖励<br />2、已邀请客户给商家好评，但好评审核未通过，好评奖励已失效", 加急费: "未在接单后24小时内完成完工条件，加急费已失效" }[e.feeName]; this.failureShow = !0 }, descriptionClick() { this.costShow = !0 }, toGetChunjieFee() { this.chunjieMoney = ""; const e = []; if (this.orderData && this.orderData.newNotTaskFeeList && this.orderData.newNotTaskFeeList.length) { this.orderData.newNotTaskFeeList.forEach(s => { s.list && s.list.length && s.list.forEach(i => { e.push(i) }) }); const t = e.find(s => s.feeName == "平台补贴"); this.chunjieMoney = t ? t.feeStr : "" } }, concealOr() { let e; this.conceal ? e = !1 : e = !0, this.conceal ? this.$emit("showConceal", e) : this.priceShow = !0 }, openGoodsShow() { this.orderData.isTmallTag == 3 ? this.jingdongShow = !0 : this.goodsShow = !0 }, closeGoodsShow(e) { e.type == "jd" ? this.jingdongShow = e.state : e.type == "goods" && (this.goodsShow = e.state) }, handleCount(e) { return e ? (e = parseFloat(e), e.toFixed(2)) : e || 0 }, goRates() { this.$router.push({ path: "/order/rates", query: { materialsData: JSON.stringify(this.materialsData) } }) }, assistMaterials() { const e = this, t = { firstCategoryId: this.orderData.cid, source: this.orderData.orderChannel, orderId: this.orderId }; h(t).then(s => { s.code == 200 ? this.materialsData = s.data : e.$toast(s.msg) }, function (s) { }) }, showLabel(e, t) { }, formattingLatel() { var s; const e = [], t = this.orderData; t.isOfflineOrder == 1 && e.push({ title: "门店", center: "来自线下门店/企业发布的订单" }), t.urgentMoney && e.push({ title: "加急", center: "请在接单后当天内完成任务则可获得加急费" }), t.ordertype == 6 && e.push({ title: "自费", center: "先支付费用，再向客户索取" }), t.ordertype == 4 && e.push({ title: "半自费", center: "上门服务后，再向客户收取部分费用" }), (t.adjustprice == 1 || t.speedOrder && ((s = t.speedOrder) == null ? void 0 : s.ptSpeedPrice) == 1) && e.push({ title: "加价", center: "商家已经增加费用的订单" }), t.isCirculationOrder == 1 && e.push({ title: "不占次数", center: "带有 不占抢单次数 的订单，不占用你每日抢单的次数，且当天抢单次数用完的情况下也可以接此类订单" }), (t.isSecondClock == 1 || t.thirdservicetype == "serviceAgain") && e.push({ title: "二次上门", center: "需二次上门进行服务" }), t.detection == 1 && e.push({ title: "检修单", center: "上门检测影响产品正常使用的问题点，若不需要更换配件，师傅需帮助调试产品，达到正常使用即可" }), t.debugOrder == 1 && e.push({ title: "调试单", center: "" }), t.offlineQuaSubsidyLabel && e.push({ title: t.offlineQuaSubsidyLabel, center: t.offlineQuaSubsidyPopStr }), t.elevenTag == 1 && e.push({ title: "双11补贴", center: "此订单支持组队补贴活动、完工抽奖活动" }), this.latelList = e } } }; var Lt = function () { var t = this, s = t._self._c; return s("div", { staticClass: "main" }, [s("div", { staticClass: "new-padd new-main-2021" }, [s("div", [s("div", { staticClass: "head" }, [s("span", { staticClass: "left" }, [t._v(" 订单金额 "), s("van-tag", { staticClass: "copy-btn concealOr", attrs: { plain: "", type: "primary" }, on: { click: t.concealOr } }, [t.conceal ? s("img", { staticClass: "img1", attrs: { src: t.imgUrl + "lbdj_app_h5/page/xianshi2.png" } }) : t._e(), t.conceal ? t._e() : s("img", { staticClass: "img2", attrs: { src: t.imgUrl + "lbdj_app_h5/page/xianshi1.png" } })])], 1)])]), s("van-divider"), s("div", { staticClass: "price-all" }, [s(t.feeComponent, { tag: "component", attrs: { "order-data": t.orderData, "show-chunjie-ad": t.showChunjieAd, "img-age": t.imgAge, conceal: t.conceal }, on: { "show-chunjie": function (i) { t.showChunjieSubsidy = !0 }, "show-info-explain": function (i) { t.balanceExplainShow = !0 }, "show-fee-check": function (i) { t.recheckFeShow = !0 }, "show-label": t.showLabel, "show-failure": t.failureClick } })], 1), t.isShowTotalFee ? s("div", { staticClass: "cost-bottom" }, [s("div", { staticClass: "cost-middle-box" }, [t._v(" 总计: "), t.conceal ? s("span", { staticStyle: { "font-weight": "bold", color: "#f33434" } }, [t._v("￥")]) : t._e(), t.conceal ? s("span", { staticClass: "b" }, [t._v(t._s(t.totalFee))]) : t._e(), t.conceal ? t._e() : s("span", [t._v("******")]), t.orderData.springActRewardsFlag == 1 && t.orderData.makeupNum ? s("div", { staticClass: "raise-price" }, [t._v(" 加价券可再加价"), s("span", { staticClass: "money" }, [t._v(t._s(t.orderData.makeupFee))]), s("span", { staticClass: "unit" }, [t._v("元")])]) : t._e()])]) : t._e(), t.labelShow ? s("confirm-dialog", { attrs: { title: t.labelName, content: t.labelTxt, type: "simple" }, on: { close: function (i) { t.labelShow = !1 } } }) : t._e(), t.priceShow ? s("confirm-dialog", { attrs: { title: "温馨提示", type: "simple", content: "服务价格为敏感信息，切勿透露给客户，否则师傅您将可能面临商家投诉" }, on: { close: t.closePriceShowDialog } }) : t._e()], 1), t.costShow ? s("cost-description", { model: { value: t.costShow, callback: function (i) { t.costShow = i }, expression: "costShow" } }) : t._e(), s("van-popup", { staticClass: "failure-show", attrs: { round: "" }, model: { value: t.failureShow, callback: function (i) { t.failureShow = i }, expression: "failureShow" } }, [s("div", { staticClass: "failure-popup" }, [s("div", { staticClass: "title" }, [t._v(t._s(t.failureReason.feeName) + "已失效")]), s("div", { staticClass: "reason" }, [s("div", { staticStyle: { color: "#1c1c1e" } }, [t._v("失效原因:")]), s("div", { directives: [{ name: "dompurify-html", rawName: "v-dompurify-html", value: t.failureReason.content, expression: "failureReason.content" }] })]), s("div", { staticClass: "know", on: { click: function (i) { t.failureShow = !1 } } }, [t._v("我知道了")])])]), s("van-popup", { staticClass: "costPopup", attrs: { round: "" }, on: { close: function (i) { t.isWin = !1 } }, model: { value: t.balanceExplainShow, callback: function (i) { t.balanceExplainShow = i }, expression: "balanceExplainShow" } }, [s("div", [s("div", { staticClass: "head" }, [t._v("增加费用/信息服务费用说明")]), s("div", { staticClass: "content" }, [s("div", [t._v("计算公式")]), s("div", [t._v("信息服务费=增加费用金额*10%")])]), s("div", { staticClass: "tips" }, [t._v("根据平台规则，增加费用收取10%的信息服务费")]), s("div", { staticClass: "btn", on: { click: function (i) { t.balanceExplainShow = !1 } } }, [t._v("我知道了")])])]), s("chunjie-subsidy", { attrs: { money: t.chunjieMoney }, model: { value: t.showChunjieSubsidy, callback: function (i) { t.showChunjieSubsidy = i }, expression: "showChunjieSubsidy" } }), t.recheckFeShow ? s("recheck-fee-dialog", { attrs: { "order-data": t.orderData }, model: { value: t.recheckFeShow, callback: function (i) { t.recheckFeShow = i }, expression: "recheckFeShow" } }) : t._e()], 1) }, Nt = [], Pt = n(Tt, Lt, Nt, !1, null, "2521cf2f", null, null); const Es = Pt.exports; const At = {
    name: "Hello", props: ["orderData", "orderId", "type", "conceal", "displayOrderFee"], components: { jingdongShow: C, goodsShow: w, simplePopup: A, tileButtonDialog: m, confirmDialog: D }, data() { return { title: "这是一个测试的数据标题", imgUrl: "https://osscdn.lbdj.com/", showPopup: !1, show: !1, goodsShow: !1, taobaoShow: !1, jingdongShow: !1, wgShow: !1, wgList: [], showWin: !0, isTmallTagName: "", url: location.origin, glShow: !1, columns: [], isGl: !0, labelShow: !1, labelTxt: "", labelName: "", isHint: !1, logistics: [], urgentCallNum: 0, isVirtual: !1, latelList: [], contactsName: "", showDr: !1, logisticsInfo: "", showToViewPopup: !1, phoneIsShow: !1, phoneType: null, isShowGk: !1, gkDiaType: "", showGk: !1, isExpire: !1 } }, created() { this.goPhone(3, 1), this.isSign(), this.getGlData(), this.urgentCallNum = this.orderData.urgentCallNum, this.orderData.isDoorStore == 1 && setTimeout(() => { this.isHint = !1, this.$emit("goTop") }, 3e3); let e; this.orderData.logisticsSingle && (e = this.orderData.logisticsSingle.replace(/\，/g, ",").replace(/\s*/g, "").split(","), this.logistics = e), this.formattingLatel() }, methods: {
        showToView() { this.showToViewPopup = !0 }, openWl() { this.showDr = !0, this.orderData.sameDayLogList && this.orderData.sameDayLogList.length > 0 && (this.showDr = !0, this.logisticsInfo = this.orderData.sameDayLogList) }, ClickOrderTag(e) { const t = JSON.parse(sessionStorage.getItem("shenCeParm")); t.Tag_name = e }, MasterContact(e, t) { const s = this.$getBuriedPointOrderBaseData(); s.page_name = "订单详情", s.phone = e, s.contacts = this.contactsName, s.contact_information = t }, formattingLatel() { var s; const e = [], t = this.orderData; t.isOfflineOrder == 1 && e.push({ title: "门店", center: "来自线下门店/企业发布的订单" }), t.urgentMoney && e.push({ title: "加急", center: "请在接单后当天内完成任务则可获得加急费" }), (t.adjustprice == 1 || ((s = t.speedOrder) == null ? void 0 : s.ptSpeedPrice) == 1) && e.push({ title: "平台补贴", center: "" }), t.ordertype == 6 && e.push({ title: "自费", center: "先支付费用，再向客户索取" }), t.ordertype == 4 && e.push({ title: "半自费", center: "上门服务后，再向客户收取部分费用" }), t.isCirculationOrder == 1 && e.push({ title: "不占次数", center: "带有 不占抢单次数 的订单，不占用你每日抢单的次数，且当天抢单次数用完的情况下也可以接此类订单" }), (t.isSecondClock == 1 || t.thirdservicetype == "serviceAgain") && e.push({ title: "二次上门", center: "需二次上门进行服务" }), t.detection == 1 && e.push({ title: "检修单", center: "上门检测影响产品正常使用的问题点，若不需要更换配件，师傅需帮助调试产品，达到正常使用即可" }), t.debugOrder == 1 && e.push({ title: "调试单", center: "" }), t.offlineQuaSubsidyLabel && e.push({ title: t.offlineQuaSubsidyLabel, center: t.offlineQuaSubsidyPopStr }), t.elevenTag == 1 && e.push({ title: "双11补贴", center: "此订单支持组队补贴活动、完工抽奖活动" }), t.repairTypeTag && t.repairTypeTag.title && e.push({ title: t.repairTypeTag.title, center: t.repairTypeTag.center }), this.latelList = e }, goUrgency() { this.$router.push({ path: "/orderHome/urgency", query: { orderId: this.orderData.id, orderSn: this.orderData.orderNum } }) }, queryLogistics(e) { this.$parent.queryLogistics(e) }, goMcc() { this.$router.push({ path: "/pages/mcc", query: {} }) }, showLabel(e, t) { if (e == "平台补贴") { this.showPopup = !0; return } t && (this.labelShow = !0, this.labelName = e, this.labelTxt = t, this.ClickOrderTag(e)) }, getGlData() { if (this.orderData.associatedOrderVos && this.orderData.associatedOrderVos.length > 0) { const e = this.orderData.associatedOrderVos; for (let t = 0; t < e.length; t++)this.columns.push({ text: e[t].orderSn, orderId: e[t].orderId }) } (this.orderData.associatedOrderVos == null || this.orderData.associatedOrderVos == "" || !this.orderData.associatedOrderVos) && (this.isGl = !1), this.orderData.associatedassociatedOrderVosOrders && this.orderData.associatedOrderVos.length < 1 && (this.isGl = !1) }, openGuanlian() { this.glShow = !0 }, onConfirm(e) { this.glShow = !1; const t = { reqUrl: location.origin + "/orderHome/myDetails?orderId=" + e.orderId + "&orderSn=" + e.text, reqType: "OPEN_WEBVIEW", data: {} }; window.sendAppData(t) }, isSign() { this.orderData.isTmallTag == 1 ? this.isTmallTagName = "" : this.orderData.isTmallTag == 2 ? this.isTmallTagName = "天猫自营" : this.orderData.isTmallTag == 3 || this.orderData.isTmallTag == 6 ? this.isTmallTagName = "京东" : this.orderData.isTmallTag == 10 && (this.isTmallTagName = "拼多多") }, goLogDebang() { this.$router.push({ path: "/orderHome/logisticsDetails", query: { orderId: this.orderData.id } }) }, goButie() { this.$router.push({ path: "/pages/subsidy", query: {} }) }, concealOr() { let e; this.conceal ? e = !1 : e = !0, this.displayOrderFee == 0 && !this.conceal ? this.$dialog.alert({ title: "温馨提示", message: "服务价格为敏感信息，切勿透露给客户，否则师傅您将可能面临商家投诉。", confirmButtonText: "知道了", messageAlign: "left", confirmButtonColor: "#FE4932" }).then(() => { this.$emit("showConceal", e) }) : this.$emit("showConceal", e) }, openGoodsShow() { this.orderData.isTmallTag == 3 ? this.jingdongShow = !0 : this.goodsShow = !0 }, closeGoodsShow(e) { e.type == "jd" ? this.jingdongShow = e.state : e.type == "goods" && (this.goodsShow = e.state) }, showWgList() { this.wgShow = !0, this.wgList = JSON.parse(this.orderData.envImgUrl) }, copyOrderButton() { const e = this, t = new p(".copy-btn"); t.on("success", () => { e.$toast.success("单号已复制"), t.destroy() }), t.on("error", () => { e.$toast.fail("该浏览器不支持自动复制"), t.destroy() }) }, goCaozuo() { this.$router.push({ path: "/clause/operationExplainLvmi", query: {} }) }, goFeedback() { this.orderData.feedbacked == 0 ? this.$router.push({ path: "/orderHome/feedback", query: { orderId: this.orderId, orderSn: this.orderData.orderNum, type: this.type } }) : this.orderData.feedbacked == 1 && (this.show = !0) }, goPhone(e, t) { const s = this, i = { orderId: this.orderData.id, type: e === 3 ? 1 : e, pageSource: t == 1 ? 1 : 2 }; e ? i.type = e === 3 ? 1 : e : i.type = this.phoneType, s.$toast.loading({ message: "获取中", mask: !0, duration: 0 }), x(i).then(a => { if (s.$toast.clear(), a.code == 200) { this.urgentCallNum = a.data.num; const { phone: o } = a.data; if (e != 3) { if (this.isMiniwx) window.location.href = "tel:" + o; else { const c = { reqType: "GET_WEB_USER_INFO_PHONE", data: { phone: o } }; window.sendAppData(c) } this.MasterContact(o, "手机号") } o && e === 3 && (this.phoneIsShow = !0) } }) }, handleContacPhone() { if (this.isMiniwx) window.location.href = "tel:" + this.orderData.technicalContactPhone; else { const e = { reqType: "GET_WEB_USER_INFO_PHONE", data: { phone: this.orderData.technicalContactPhone } }; window.sendAppData(e) } this.contactsName = "技术支持", this.MasterContact(this.orderData.technicalContactPhone, "手机号") },


        getPhone(e) {
             this.contactsName = e;
              let t = "";
               if (e == "下单人") {
                 t = 1, this.getHomeVirtual();
                  return 
                }
                 else e == "联系技术支持" ? t = 2 : t = ""; 
                 this.orderData.virtualCustomerPhoneFlag != 1 && this.orderData.isHideCustomerPhone == 1 && this.orderData.lubanPrivacyUse === 1 ? (this.isVirtual = !0, this.phoneType = t) : this.goPhone(t) },
                 getHomeVirtual() {
                     const e = this, 
                    t = {
                         telB: this.orderData.contactTel, 
                        orderId: this.orderData.id, type: 2 };
                         e.$toast.loading({ message: "获取中", mask: !0, duration: 0 }), 
                         S(t).then(s => { var i, a; if (e.$toast.clear(), s.code == 200) if ((i = s.data) != null && i.isPrivacy) I({ orderId: this.orderData.id }).then(o => { var c; if (o.data) this.isExpire = !0; else { const r = { reqType: "GET_WEB_USER_INFO_PHONE", data: { phone: (c = s.data) == null ? void 0 : c.phone } }; window.sendAppData(r) } }); else { const o = { reqType: "GET_WEB_USER_INFO_PHONE", data: { phone: (a = s.data) == null ? void 0 : a.phone } }; window.sendAppData(o) } }, function (s) { e.$toast.fail(s.msg) }) }, goPzlm() { this.$router.push({ path: "/pages/pzlm", query: {} }) }, showGkDia(e) { this.isShowGk = !0, this.gkDiaType = e }, 
                 cancelGkDia() { this.isShowGk = !1, this.gkDiaType = "" }
    }
}; var Ot = function () { var i; var t = this, s = t._self._c; return s("div", { staticClass: "main" }, [s("div", { staticStyle: { clear: "both" } }), s("div", { staticClass: "new-padd new-main-2021" }, [s("div", { staticClass: "new-title-order-info" }, [s("div", { staticClass: "new-title-left" }, [t._v(" 订单信息 "), t.orderData.heightInsureFeeFlag == 1 ? [s("div", { staticClass: "label-li", on: { click: function (a) { return t.showGkDia(2) } } }, [t._v(" 渠道单"), s("van-icon", { staticStyle: { top: "0.03rem" }, attrs: { name: "arrow" } })], 1)] : t._e(), t.isTmallTagName ? s("span", { staticClass: "tab" }, [t._v(t._s(t.isTmallTagName))]) : t._e(), t.orderData.isFurniturePslxdjczVip == 1 ? s("span", { staticClass: "tab", on: { click: t.goButie } }, [t._v("已补贴4%")]) : t._e(), t.orderData.isOfflineOrder == 1 ? s("span", { staticClass: "tab", on: { click: t.goMcc } }, [t._v("补贴5%")]) : t._e(), t.orderData.discountFlagActivity == 1 ? s("span", { staticClass: "tab" }, [t._v("9.5折服务月")]) : t._e(), t.orderData.priorityOrderTag == 1 ? s("div", { staticClass: "tab-img" }, [s("img", { attrs: { src: t.imgUrl + "lbdj_app_h5/newUi/2021/icon_yx.png" }, on: { click: function (a) { return t.showLabel("优享订单", "优享订单仅提供给优享品质师傅（高收入高要求0售后）") } } })]) : t._e(), t.orderData.vipDiscountTag == 1 && t.orderData.isFurniturePslxdjczVip === 0 && t.orderData.isOfflineOrder != 1 ? s("div", { staticClass: "tab-img" }, [s("img", { attrs: { src: t.imgUrl + "lbdj_app_h5/newUi/2021/icon_pzlm.png" }, on: { click: t.goPzlm } })]) : t._e(), t.orderData.incrSalaryPlatMark == 1 ? s("span", { staticClass: "tab" }, [t._v("急速报价")]) : t._e(), t.latelList.length ? t._l(t.latelList, function (a, o) { return s("div", { key: o, staticClass: "label-li", on: { click: function (c) { return t.showLabel(a.title, a.center) } } }, [t._v(" " + t._s(a.title)), s("van-icon", { staticStyle: { top: "0.03rem" }, attrs: { name: "arrow" } })], 1) }) : t._e(), t.isGl ? s("span", { staticClass: "relevance", on: { click: t.openGuanlian } }, [t._v("查看关联订单")]) : t._e()], 2)]), s("van-divider"), s("div", { staticClass: "new-list" }, [t.orderData.states >= 2 ? [t.phoneIsShow && t.orderData.contactTel ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("下单电话")]), s("div", { staticClass: "li-txt rt" }, [s("van-tag", { staticClass: "copy-btn", staticStyle: { width: "auto" }, attrs: { plain: "", type: "primary" }, on: { click: function (a) { return t.getPhone("下单人") } } }, [t._v(" 联系下单方 ")])], 1), s("div", { staticClass: "co" })]) : t._e(), t.orderData.contactName ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("下单用户")]), s("div", { staticClass: "li-txt rt" }, [t._v(t._s(t.orderData.contactName))]), s("div", { staticClass: "co" })]) : t._e()] : t._e(), t.orderData.technicalContactPhone ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("技术联系电话")]), s("div", { staticClass: "li-txt rt" }, [s("span", { staticClass: "btns", on: { click: function (a) { return t.getPhone("联系技术支持") } } }, [t._v("联系技术支持")])]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.technicalContact ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("技术联系人")]), s("div", { staticClass: "li-txt rt" }, [t._v(t._s(t.orderData.technicalContact))]), s("div", { staticClass: "co" })]) : t._e(), s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("订单编号")]), s("div", { staticClass: "li-txt rt" }, [s("span", { attrs: { id: "copy_text" } }, [t._v(t._s(t.orderData.orderNum))]), s("span", { staticClass: "copy-btn", attrs: { "data-clipboard-action": "copy", "data-clipboard-target": "#copy_text" }, on: { click: t.copyOrderButton } }, [t._v(" 复制 ")])]), s("div", { staticClass: "co" })]), s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("下单时间")]), s("div", { staticClass: "li-txt rt" }, [s("span", { attrs: { id: "copy_text" } }, [t._v(t._s(t.orderData.createTime || t.orderData.createtime))])]), s("div", { staticClass: "co" })]), [0, 1].includes(t.orderData.workHighTypeTag) ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("作业环境")]), s("div", { staticClass: "li-txt rt flex-center" }, [s("span", [t._v("该订单为高空作业订单")]), s("img", { staticClass: "tip", attrs: { src: "https://osscdn.lbdj.com/J/G/G-A/devCustom/20241008/workHighFee/tip.png" }, on: { click: function (a) { t.showGk = !0 } } })]), s("div", { staticClass: "co" })]) : t._e(), ["cz", "ps"].includes(t.orderData.servicetype) ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("楼层信息")]), s("div", { staticClass: "li-txt rt" }, [s("span", { attrs: { id: "copy_text" } }, [t._v(" " + t._s(t.orderData.elevator > 0 ? "有电梯" : "没有电梯") + " "), t.orderData.floor != 0 ? [t._v(" (" + t._s(t.orderData.floor) + "层) ")] : t._e()], 2)]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.packagenum ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("包裹数量")]), s("div", { staticClass: "li-txt rt" }, [t._v(" " + t._s(t.orderData.packagenum) + " ")]), s("div", { staticClass: "co" })]) : t._e(), (t.orderData.selforder == 0 || t.orderData.selforder == 1) && t.orderData.islvmilockorder ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("来源渠道")]), s("div", { staticClass: "li-txt rt" }, [t._v(" " + t._s(t.orderData.selforder == 1 ? "自营单" : "普通单") + " ")]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.customerDesc ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("客户描述")]), s("div", { staticClass: "li-txt rt li-remark" }, [t._v(t._s(t.orderData.customerDesc))]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.envRemark ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("环境信息")]), s("div", { staticClass: "li-txt rt" }, [t._v(" " + t._s(t.orderData.envRemark) + " "), t.orderData.envImgUrl ? s("van-tag", { staticClass: "copy-btn", attrs: { plain: "", type: "primary" }, on: { click: function (a) { return t.showWgList() } } }, [t._v(" 查看 ")]) : t._e()], 1), s("div", { staticClass: "co" })]) : t._e(), t.orderData.otherRequire ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title" }, [t._v("其他要求")]), s("div", { staticClass: "li-txt li-remarks hh" }, [t._v(t._s(t.orderData.otherRequire))]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.note ? s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("备忘录")]), s("div", { staticClass: "li-txt rt hh", staticStyle: { "word-break": "break-all", "word-wrap": "break-word" } }, [t._v(" " + t._s(t.orderData.note) + " ")]), s("div", { staticClass: "co" })]) : t._e(), t.orderData.cid == 130 ? s("div", [s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("期望进场时间")]), s("div", { staticClass: "li-txt rt" }, [t._v(t._s(t.orderData.beginTime))]), s("div", { staticClass: "co" })]), s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("施工周期")]), s("div", { staticClass: "li-txt rt" }, [t._v(t._s(t.orderData.projectPeriod))]), s("div", { staticClass: "co" })]), s("div", { staticClass: "list-li" }, [s("div", { staticClass: "li-title lf" }, [t._v("需要发票")]), s("div", { staticClass: "li-txt rt" }, [t._v(" " + t._s(t.orderData.needBill == 1 ? "是" : "否") + " ")]), s("div", { staticClass: "co" })])]) : t._e()], 2), t.orderData.entrustPaymentAmount && t.orderData.entrustPaymentStatus ? s("div", { staticClass: "new-order-li" }, [s("h4", [t._v("代收货款：" + t._s(t.orderData.entrustPaymentAmount) + "元")]), s("p", [t._v("代收货款金额为帮助商家向客户收取货款，师傅上门后向客户出示鲁班收款码，不得以其它方式收款")])]) : t._e()], 1), s("van-dialog", { attrs: { title: "反馈信息", "confirm-button-text": "关闭", "confirm-button-color": "#F33434" }, model: { value: t.show, callback: function (a) { t.show = a }, expression: "show" } }, [s("div", { staticClass: "show-main" }, [s("p", [t._v(" 问题类型： "), s("span", [t._v(t._s(t.orderData.feedbackType == 1 ? "订单信息不明确" : t.orderData.feedbackType == 2 ? "需要增加附加费" : (t.orderData.feedbackType == 3, "其他")))])]), s("p", [t._v(" 具体信息： "), s("span", { staticStyle: { "word-break": "break-all", "word-wrap": "break-word" } }, [t._v(t._s(t.orderData.feedbackComment))])])])]), s("van-dialog", { attrs: { title: "淘宝订单结款须知", "confirm-button-text": "我知道了", "confirm-button-color": "#F33434" }, model: { value: t.taobaoShow, callback: function (a) { t.taobaoShow = a }, expression: "taobaoShow" } }, [s("div", { staticClass: "show-main" }, [s("div", { staticClass: "show-p" }, [s("p", [s("b", [t._v("什么是淘宝订单？")])]), s("p", [t._v(" 淘宝订单是来自于" + t._s(t.G_APP_NAME) + "战略合作伙伴《淘宝网》的服务订单，必须经过客户确认核销才可以正常结款。 ")])]), s("div", { staticClass: "show-p" }, [s("p", [s("b", [t._v("淘宝订单如何确认核销并结算费用？")])]), s("p", [t._v(" 淘宝订单安装完成后，师傅必须引导客户在手机淘宝客户端操作确认核销才可以进行结算。操作方法：①打开手机淘宝APP;②选择对应产品订单，点击进入上门安装服务页面；③点击确认完成。（注：是点击上门安装服务确认完成，而非“确认收货”） ")])])])]), s("van-image-preview", { attrs: { images: t.wgList }, model: { value: t.wgShow, callback: function (a) { t.wgShow = a }, expression: "wgShow" } }), t.goodsShow ? s("goods-show", { on: { closeWin: t.closeGoodsShow } }) : t._e(), t.jingdongShow ? s("jingdong-show", { on: { closeWin: t.closeGoodsShow } }) : t._e(), s("van-popup", { attrs: { position: "bottom" }, model: { value: t.glShow, callback: function (a) { t.glShow = a }, expression: "glShow" } }, [s("van-picker", { attrs: { "show-toolbar": "", columns: t.columns }, on: { cancel: function (a) { t.glShow = !1 }, confirm: t.onConfirm } })], 1), t.labelShow ? s("confirm-dialog", { attrs: { title: t.labelName, content: t.labelTxt, type: "simple" }, on: { close: function (a) { t.labelShow = !1 } } }) : t._e(), t.isVirtual ? s("confirm-dialog", { attrs: { title: "请确认您的手机号码", content: t.orderData.workerPhone + "该号码将被加密保护" }, on: { close: function (a) { t.isVirtual = !1 }, confim: t.goPhone } }) : t._e(), s("van-dialog", { attrs: { title: "物流信息", "confirm-button-text": "我知道了", "confirm-button-color": "#FE4932" }, on: { confirm: function (a) { t.showWl = !1 } }, model: { value: t.showDr, callback: function (a) { t.showDr = a }, expression: "showDr" } }, [s("div", { staticClass: "wl-main" }, [s("div", { staticClass: "wl-list" }, [t.logisticsInfo ? s("van-steps", { attrs: { direction: "vertical", active: 0, "active-color": "#FE4932" } }, t._l(t.logisticsInfo, function (a, o) { return s("van-step", { key: o }, [s("h3", [t._v(t._s(a.remark))]), s("p", [t._v(t._s(a.createTime))])]) }), 1) : t._e(), t.logisticsInfo ? t._e() : s("div", { staticClass: "wl-none" }, [t._v("暂无物流信息")])], 1)])]), t.showPopup ? s("confirm-dialog", { attrs: { type: "simple", title: "平台补贴" }, on: { close: function (a) { t.showPopup = !1 } } }, [s("div", { staticClass: "simple-popup-content" }, [t._v(" 平台已补贴"), s("span", { staticStyle: { color: "#f33434" } }, [t._v(t._s(((i = t.orderData.speedOrder) == null ? void 0 : i.orderSpeedFee) || t.orderData.adjustFee))]), t._v("元，师傅接单完工后即可获得平台补贴 ")])]) : t._e(), s("van-dialog", { attrs: { "show-confirm-button": !1, width: "6rem" }, model: { value: t.isShowGk, callback: function (a) { t.isShowGk = a }, expression: "isShowGk" } }, [t.gkDiaType == 2 ? s("div", { staticClass: "gk-tip-text" }, [t._v(" 匠心渠道商是鲁班到家战略合作伙伴，拥有上千家商家合作渠道，同时和平台紧密合作，为平台和师傅提供优质的订单和服务。 ")]) : t._e(), s("div", { staticClass: "gk-footer-btn", on: { click: t.cancelGkDia } }, [t._v("我知道了")])]), s("tile-button-dialog", { attrs: { title: "高空作业提示", message: "师傅必须佩戴安全绳、安全带或者安装防护网架等有效的安全设施设备。作业时需配备安全员，拉起警戒线，严格按照国家安全生产管理部门的规定进行作业操作，以免造成自身或第三人的财产损失。", "confirm-button-text": "我知道了", "content-in-center": !1 }, on: { confirm: function (a) { t.showGk = !1 } }, model: { value: t.showGk, callback: function (a) { t.showGk = a }, expression: "showGk" } }), s("van-dialog", { attrs: { title: "客户号码已失效", message: "客户的电话号码已失效，如您希望与客户取得联系，请联系平台处理", "confirm-button-text": "知道了", "confirm-button-color": "#48A4FF" }, on: { confirm: function (a) { t.isExpire = !1 } }, model: { value: t.isExpire, callback: function (a) { t.isExpire = a }, expression: "isExpire" } })], 1) }, jt = [], Rt = n(At, Ot, jt, !1, null, "a4fcc98a", null, null); const Gs = Rt.exports; const Et = { name: "Hello", props: ["orderInfo"], data() { return { title: "这是一个测试的数据标题", imgUrl: "https://osscdn.lbdj.com/lbdj_app_h5/page/" } }, created() { }, methods: { goDeposit() { const e = { reqType: "TO_PAGE", data: { appPage: 0, description: "保证金首页" } }; window.sendAppData(e) } } }; var Gt = function () { var t = this, s = t._self._c; return s("div", { staticClass: "main" }, [s("div", { staticClass: "new-padd" }, [s("div", { staticClass: "hx-title" }, [t.orderInfo.tmVerify == 1 ? [t.orderInfo.newTmallOrder == 1 ? [s("img", { attrs: { src: "https://osscdn.lbdj.com/lbdj_app_h5/order/miaoshifu.png" } }), s("p", [t._v("喵师傅信息")])] : [s("img", { attrs: { src: "https://osscdn.lbdj.com/lbdj_app_h5/order/miaoshifu.png" } }), s("p", [t._v("喵师傅信息")])]] : t._e(), t.orderInfo.tmVerify == 2 ? [s("img", { attrs: { src: t.imgUrl + "wang.png" } }), s("p", [t._v("汪师傅信息")])] : t._e(), s("div", { staticClass: "co" })], 2), s("div", { staticClass: "hx-txt" }, [t.orderInfo.tmVerify == 1 ? [t.orderInfo.newTmallOrder == 1 ? s("p", [t._v("该订单需要进行喵师傅核销才能完结订单")]) : s("p", [t._v("该订单需要进行喵师傅核销才能完结订单")])] : t._e(), t.orderInfo.tmVerify == 2 ? [s("p", [t._v("该订单需要进行汪师傅核销才能完结订单")])] : t._e(), s("p", [t._v("核销单号：" + t._s(t.orderInfo.states >= 3 ? t.orderInfo.souceOrderId : "******（接单后可见）"))])], 2)])]) }, Bt = [], qt = n(Et, Gt, Bt, !1, null, "e29bb6e1", null, null); const Bs = qt.exports; const Mt = { props: ["orderInfo", "triangle"], name: "Accessory", data() { return { imgSrc: "https://osscdn.lbdj.com/lbdj_app_h5/mall/partsMall/new/", explainShow: !1 } }, mounted() { }, methods: { explain() { this.explainShow ? this.explainShow = !1 : this.explainShow = !0 } } }; var Vt = function () { var t = this, s = t._self._c; return t.orderInfo.isShowAccessoryBtn === 1 ? s("div", { staticClass: "Accessory" }, [s("div", { staticClass: "text" }, [t._v(" 必要条件：商家要求，为避免二次上门，请您提前采购"), s("span", { staticClass: "act" }, [t._v(t._s(t.orderInfo.accessoryBrand))]), t._v(" 的配件 "), s("img", { attrs: { src: t.imgSrc + "explain.png", alt: "" }, on: { click: t.explain } }), t.explainShow ? s("div", { staticClass: "details" }, [t._v(" *请提前在配件商城中购买相关配件并在上门服务时携带配件，服务中申请增加配件费用，再选择对应消耗掉的配件型号即可 ")]) : t._e(), t.explainShow ? s("div", { staticClass: "details-bg", on: { click: t.explain } }) : t._e()]), s("div", { staticClass: "bottom-m" }, [s("div", { staticClass: "button", on: { click: function (i) { return t.$router.push("/accessoriesMall/homeNew") } } }, [t._v("配件商城")])])]) : t._e() }, Ut = [], zt = n(Mt, Vt, Ut, !1, null, "09d3eab5", null, null); const qs = zt.exports; const Wt = { props: { orderId: { type: String, default: "" } }, data() { return { message: "", show: !1 } }, methods: { open() { this.show = !0, this.getAppToken().then(e => { this.getData() }) }, getData() { const e = this, t = { orderId: this.orderId }; e.$toast.loading({ message: "加载中", mask: !0, duration: 0 }), g(t).then(s => { e.$toast.clear(), s.code == 200 && s.data.length > 0 && (this.message = s.data[0].remark) }, function (s) { }) }, submitData() { const e = this; if (this.message == "") return e.$toast("请填写备注信息"), !1; const t = { orderId: this.orderId, remark: this.message }; e.$toast.loading({ message: "提交中", mask: !0, duration: 0 }), $(t).then(s => { e.$toast.clear(), s.code == 200 ? (e.$toast.success("提交成功"), this.show = !1, this.$emit("get-remark")) : e.$toast(s.msg), this.SubmitOrderNotes(s.code, s.msg) }, function (s) { e.SubmitOrderNotes(-1, s.msg), e.$toast.fail(s.msg) }) }, SubmitOrderNotes(e, t) { const s = this.$getBuriedPointOrderBaseData(); s.is_success = e == 200, s.fail_reason = t } } }; var Ht = function () { var t = this, s = t._self._c; return s("van-popup", { staticClass: "order-remark-popup", attrs: { "get-container": "body", position: "bottom", "safe-area-inset-bottom": "" }, model: { value: t.show, callback: function (i) { t.show = i }, expression: "show" } }, [s("div", { staticClass: "header" }, [s("div", { staticClass: "title" }, [t._v("订单备注")])]), s("div", { staticClass: "main" }, [s("div", { staticClass: "textarea-div" }, [s("van-field", { staticClass: "remark-input", class: { "error-text": t.message.length == 200, "normal-text": t.message.length && t.message.length < 200 }, attrs: { rows: "3", type: "textarea", maxlength: "200", placeholder: "请输入订单备注", "show-word-limit": "" }, model: { value: t.message, callback: function (i) { t.message = i }, expression: "message" } }), s("div", { staticClass: "clear-text", style: t.message.length == 0 && "color:#8E8E93", on: { click: function (i) { t.message = "" } } }, [t._v("清空")])], 1), s("div", { staticClass: "tip-box" }, [s("div", { staticClass: "tip-title" }, [t._v("温馨提示")]), s("div", { staticClass: "tip-content" }, [s("p", [t._v("1、支持多次添加备注")]), s("p", [t._v("2、每次提交的备注内容可前往【操作记录】中查看")])])])]), s("div", { staticClass: "footer" }, [s("div", { staticClass: "btn cancel-btn", on: { click: function (i) { t.show = !1 } } }, [t._v("返回")]), s("div", { staticClass: "btn", on: { click: t.submitData } }, [t._v("提交")])])]) }, Qt = [], Jt = n(Wt, Ht, Qt, !1, null, "ea03b0d3", null, null); const Kt = Jt.exports; const Yt = { components: { headRebate: j, orderRemark: Kt, customerService: R }, name: "Hello", props: ["orderInfo", "orderId", "orderSn", "type", "rebateList", "conditions"], data() { return { title: "", txt: "", txts: "", isGoStatus: !1, tab: "", imgUrl: "https://osscdn.lbdj.com/lbdj_app_h5/newUi/2021/", isStatesImg: "", countDown: "", ratio: "", rebateLookShow: !1, remarkText: "备注" } }, created() { this.getData(), this.getDatas(), "".concat(this.title, "详情页") }, methods: { goBack() { if (this.$route.query.isBack) this.$route.query.isBack == 1 && this.$router.go(-1), this.$route.query.isBack == 2 && this.$router.go(-2); else { if (!this.conditions) return this.pageBack(); F({ floatingWindowCode: "logic_order_three_1", extend: this.orderId }).then(e => { e.data ? this.$emit("showPopup") : this.pageBack() }).catch(() => { this.pageBack() }) } }, pageBack() { if (this.$route.query.goBack) this.$router.back(); else { const e = { reqType: "GO_BACK", data: {} }; window.sendAppData(e) } }, godoubleEleven() { this.$router.push("/act/doubleEleven2022?p=8") }, getDatas() { const e = this, t = { orderId: e._props.orderId }; e.$toast.loading({ message: "加载中", mask: !0, duration: 0 }), g(t).then(s => { e.$toast.clear(), s.code == "200" && (s.data.length == 0 ? e.remarkText = "备注" : e.remarkText = "已备注") }) }, onRemarks() { this.$refs.orderRemarkRef.open(), O("click_OrderDetails_Remarks") }, goMore() { (this.orderInfo.isException == 0 || this.orderInfo.exceptionReason == 13) && this.goStatus() }, getData() { this.orderInfo.states < 3 && (this.orderInfo.cid == 130 ? this.title = this.orderInfo.catName + "工程项目订单" : this.title = this.orderInfo.catName + this.orderInfo.type, this.orderInfo.grabremainTime, this.type == 1 ? this.txt = this.orderInfo.quotedMoney ? "待雇佣" : "待报价" : this.txt = "待接单", this.orderInfo.ordertype == 3 && !this.orderInfo.busofferno && (this.tab = "议价")), this.orderInfo.states == 3 && (this.title = "待预约", this.txt = this.orderInfo.promptMsg, this.isStatesImg = 1), this.orderInfo.states == 5 && (this.orderInfo.isPS == 1 && this.orderInfo.isSign == 0 && this.orderInfo.cid != 130 ? (this.orderInfo.isOfflineOrder == 1 ? (this.title = "待提货", this.isStatesImg = 2) : (this.title = "待签收", this.isStatesImg = 3), this.txt = this.orderInfo.promptMsg) : this.orderInfo.clockFinish == 0 ? (this.title = "待上门", this.txt = this.orderInfo.promptMsg, this.isStatesImg = 4) : this.orderInfo.clockFinish == 1 && (this.title = "服务中", this.txt = "订单正在服务中，请按要求服务", this.isStatesImg = 5)), this.orderInfo.states == 6 && (this.title = "待验收", this.txt = "订单已完工，正在等待验收", this.isStatesImg = 6), this.orderInfo.states == 8 && (this.title = "待打款", this.txt = this.orderInfo.heightInsureFeeFlag == 1 ? "渠道商将于24小时内为您打款，请耐心等待" : "平台将于24小时内为您打款，请耐心等待", this.isStatesImg = 7), this.orderInfo.states == 10 && (this.title = "已结单", this.txt = "平台已付款，请到钱包查收", this.isStatesImg = 8, this.orderInfo.tryOrderFrozenTime && (this.txts = "因您处于试单阶段，该订单金额将暂时冻结于钱包，您可于" + this.orderInfo.tryOrderUnFrozenTime + "提现")), this.orderInfo.states == 12 && (this.title = "已关闭", this.txt = "订单已关闭"), this.orderInfo.isPause == 1 && (this.title = "订单已挂起，等待继续服务", this.txt = "对订单有疑问，请直接联系客户"), (this.orderInfo.isException == 0 || this.orderInfo.exceptionReason == 13) && (this.isGoStatus = !0) }, goStatus() { sessionStorage.setItem("orderInfo", JSON.stringify(this.orderInfo)), this.$router.push({ path: "/orderHome/orderDetailLogs", query: { orderId: this.orderId } }) } } }; var Xt = function () { var t = this, s = t._self._c; return s("div", { staticClass: "main" }, [s("div", [s("head-rebate", { attrs: { "order-id": t.orderId, "order-cat": t.orderInfo.orderCat, "is-states-img": t.isStatesImg } })], 1), s("div", { staticClass: "new-status-my", class: t.orderInfo.states < 3 ? "order-status" : "", on: { click: t.goMore } }, [t.isMiniwx ? t._e() : s("img", { staticClass: "arrow-left", attrs: { src: "https://osscdn.lbdj.com/J/G/G-A/devCustom/20240619/common/back-black.png" }, on: { click: t.goBack } }), t.orderInfo.states >= 3 ? [s("div", { staticClass: "status-center" }, [s("div", { staticClass: "status-title" }, [s("div", { staticClass: "status-title-text" }, [t._v(" " + t._s(t.title) + " "), s("span", { staticClass: "home-logo" }, [t._v("优享订单")])])]), t.txt ? s("p", [t._v(" " + t._s(t.txt) + " "), t.isGoStatus ? s("van-icon", { staticStyle: { "vertical-align": "-2px" }, attrs: { color: "#8e8e93", name: "arrow" } }) : t._e()], 1) : t._e(), s("p", [t._v(t._s(t.txts))]), t.orderInfo.isPause == 1 ? s("customer-service", { attrs: { "order-info": t.orderInfo, "use-simple": !0 } }, [s("div", { staticClass: "contact-wrap" }, [s("img", { attrs: { src: "https://osscdn.lbdj.com/upload/order/2023-07-13-03-49-57/E70CA69AF8FEC343062815744AF5B2C4/kefu.png", alt: "" } }), s("span", { staticClass: "text" }, [t._v("客服")])])]) : t._e()], 1)] : [s("div", { staticClass: "status-center" }, [s("div", { staticClass: "status-title status-title-tag" }), s("p", { staticClass: "top-title" }, [t._v(t._s(t.txt)), s("span", { staticClass: "home-logo" }, [t._v("优享订单")])]), s("p", [t._v(t._s(t.txts))]), s("div", { staticClass: "new-sign" }, [s("div", { staticClass: "sign-bzj" }, [t._v("诚信保证金师傅")]), s("div", { staticClass: "sign-priority" }, [t._v(" " + t._s(t.type == 0 ? "抢单优先" : t.type == 1 ? "雇佣优先" : t.type == 2 ? "推送优先" : t.type == 3 ? "抢单优先" : "") + " ")]), s("div", { staticClass: "lable-cont" }, [t.orderInfo.isPriorityPushTwo ? s("div", { staticClass: "lable-box" }, [s("img", { staticClass: "lable-box-img", attrs: { src: "https://osscdn.lbdj.com/wechat/index/img005.png" } }), s("span", [t._v("标兵优先")])]) : t._e()]), t.orderInfo.incrSalaryPlatMark ? s("div", { staticClass: "to-speed-offer" }, [t._v("加薪大计划")]) : t._e()])])]], 2), s("order-remark", { ref: "orderRemarkRef", attrs: { "order-id": t.orderId }, on: { "get-remark": t.getDatas } })], 1) }, Zt = [], ts = n(Yt, Xt, Zt, !1, null, "3c83cc48", null, null); const Ms = ts.exports; const ss = { components: { wxhirePopup: E }, name: "RebatePrompt", props: ["orderId", "orderInfo"], data() { return { servant: "", title: "" } }, created() { this.orderId && this.orderInfo && (this.orderInfo.workerId ? this.orderInfo.workerId : this.orderInfo.workerid) }, methods: { async AppRebateOrderDetailTaskInfoApiRequest(e, t) { const { ordertype: s } = JSON.parse(sessionStorage.getItem("orderInfo")); T({ orderId: e, workerId: t, clickSource: [0, "0", 1, "1"].includes(s) ? s : null }).then(i => { i.code == "200" && i.data && (this.servant = i.data, this.$emit("servant", this.servant)) }) }, async getSendAppData(e) { if (this.isMiniwx) this.currentOrderId = e, setTimeout(() => { this.$refs.wxhirePopup.openshow() }, 0); else { const t = { reqType: "OPEN_REBATE_LIST", data: { orderId: e } }; window.sendAppData(t), this.orderInfo && (this.orderInfo.states == 3, this.orderInfo.states == 5 && (this.orderInfo.clockFinish == 0, this.orderInfo.clockFinish == 1), this.orderInfo.states == 6) } }, getData(e) { switch (e) { case 1: return "报价/接单"; case 2: return "内完成预约客户，可获得返佣"; default: return "内引导业主在小程序中完成好评，可获得返佣" } }, goGood() { this.$router.push({ path: "/pages/2021/page0821", query: {} }) } } }; var es = function () { var t = this, s = t._self._c; return s("div", { staticClass: "mains" }, [t.servant ? s("div", { staticClass: "rebate_prompt", on: { click: function (i) { return i.stopPropagation(), t.getSendAppData(t.servant.orderId) } } }, [s("div", {}, [s("div", { staticClass: "rebate_top" }, [s("div", { staticClass: "top_left" }, [s("span", [t._v(t._s(t.servant.rebateTag))])]), s("div", { staticClass: "top_right" }, [s("span", [t.servant.taskOver == 0 ? [t._v(" 共"), s("span", [t._v(t._s(t.servant.taskNum))]), t._v("个任务，完成"), s("span", [t._v(t._s(t.servant.taskCompleteNum))]), t._v("个，获得返佣"), s("span", [t._v(t._s(t.servant.ownRebateRate) + "%")])] : t._e(), t.servant.taskOver != 0 && t.servant.taskNum > 1 ? [t._v(" 共"), s("span", [t._v(t._s(t.servant.taskNum))]), t._v("个任务，最高可返佣"), s("span", [t._v(t._s(t.servant.totalHighestRebateRate) + "%")])] : t._e(), t.servant.taskOver != 0 && t.servant.taskNum == 1 ? [t._v(" 共"), s("span", [t._v(t._s(t.servant.taskNum))]), t._v("个任务，可返佣"), s("span", [t._v(t._s(t.servant.currentRebateRate) + "%")])] : t._e()], 2)])]), t.servant.taskOver != 0 ? s("div", { staticClass: "rebate_bottom" }, [s("div", { staticClass: "count_down" }, [t.servant.taskOver == 1 ? [t.servant.effectiveMillisecond > 0 ? s("span", [s("van-count-down", { attrs: { time: t.servant.effectiveMillisecond, format: "HH时mm分ss秒" } })], 1) : t._e(), s("span", { staticClass: "text" }, [t._v(t._s(t.servant.taskName))])] : t._e(), t.servant.taskOver == 2 ? [s("span", { staticClass: "text" }, [t._v(t._s(t.servant.taskName))])] : t._e()], 2)]) : t._e()]), s("van-icon", { attrs: { name: "arrow", size: "16px", color: "#8e8e93" } })], 1) : t._e(), t.orderInfo.states <= 5 && t.orderInfo.makeGoodMoney ? s("div", { staticClass: "return-money", class: t.servant && "b-t", on: { click: t.goGood } }, [s("div", { staticClass: "good-main" }, [s("div", { staticClass: "good-lf" }, [s("span", { staticClass: "top_left" }, [t._v("好评奖励")]), s("p", [t._v("邀请客户为下单方店铺好评额外得" + t._s(t.orderInfo.makeGoodMoney) + "元")])])]), s("van-icon", { attrs: { name: "arrow", size: "16px", color: "#8e8e93" } })], 1) : t._e(), s("wxhire-popup", { ref: "wxhirePopup", staticClass: "hirePopup", attrs: { id: t.orderId } })], 1) }, as = [], is = n(ss, es, as, !1, null, "d9957e60", null, null); const Vs = is.exports; const os = { name: "HelloWorld", props: ["orderInfo", "type"], data() { return { title: "", time: 1e6, winShow: !1, winTxt: "" } }, created() { }, methods: { hint(e) { var t; this.title = e == 1 ? "高额补贴" : "平台补贴", this.winTxt = e == 1 ? "平台专属补贴订单，完工后和订单费用一起结算，该补贴奖励不抽佣" : "平台已补贴<span style='color:#F33434'>".concat(((t = this.orderInfo.speedOrder) == null ? void 0 : t.orderSpeedFee) || this.orderInfo.adjustFee, "元</span>，师傅接单完工后即可获得平台补贴"), this.winShow = !0 } } }; var rs = function () { var i; var t = this, s = t._self._c; return s("div", { staticClass: "main" }, [s("div", { staticClass: "new-padd new-main-2021" }, [t.orderInfo.highFillLabel && t.orderInfo.highFillLabel.length > 0 ? s("div", { staticClass: "gb" }, [s("div", { staticClass: "sub-title" }, [s("p", { staticClass: "p1" }, [t._v(" 高额补贴奖励 "), s("van-icon", { staticClass: "gbicon", attrs: { name: "question-o", size: "14" }, on: { click: function (a) { return t.hint(1) } } })], 1)]), s("van-divider"), t.orderInfo.highFillLabel ? s("div", { staticClass: "sub-list" }, t._l(t.orderInfo.highFillLabel, function (a, o) { return s("span", { key: o }, [t._v(t._s(a))]) }), 0) : t._e()], 1) : t._e(), (i = t.orderInfo.speedOrder) != null && i.orderSpeedFee && t.orderInfo.highFillLabel && t.orderInfo.highFillLabel.length > 0 || t.orderInfo.adjustFee && t.orderInfo.highFillLabel && t.orderInfo.highFillLabel.length > 0 ? s("div", { staticClass: "line" }) : t._e()]), s("div", { staticClass: "price-box" }, [s("van-popup", { attrs: { round: "" }, model: { value: t.winShow, callback: function (a) { t.winShow = a }, expression: "winShow" } }, [s("div", { staticClass: "price-show" }, [s("div", { staticClass: "title" }, [t._v(t._s(t.title))]), s("div", { directives: [{ name: "dompurify-html", rawName: "v-dompurify-html", value: t.winTxt, expression: "winTxt" }], staticClass: "msg" }), s("div", { staticClass: "btn", on: { click: function (a) { t.winShow = !1 } } }, [t._v("我知道了")])])])], 1)]) }, ls = [], ns = n(os, rs, ls, !1, null, "baecf3ff", null, null); const Us = ns.exports; const cs = { props: ["orderInfo", "orderId"], components: { "parts-dialog": () => _(() => import("./parts-dialog-46686b6a.js"), ["assets/parts-dialog-46686b6a.js", "assets/index-6623cdb9.js", "assets/index-5be45467.css", "assets/clipboard-80022da2.js", "assets/parts-dialog-6847e467.css"]), "service-details": () => _(() => import("./service-details-7109aefc.js"), ["assets/service-details-7109aefc.js", "assets/index-6623cdb9.js", "assets/index-5be45467.css", "assets/service-details-e1dadd5d.css"]), "lock-info-dialog": () => _(() => import("./lock-info-dialog-f95f3647.js"), ["assets/lock-info-dialog-f95f3647.js", "assets/index-6623cdb9.js", "assets/index-5be45467.css", "assets/lock-info-dialog-568a7fe7.css"]) }, data() { return { title: "", txt: "", isShow: !1, videoImg: "https://osscdn.lbdj.com/pc/img/common/video-icon.png", imgUrl: "https://osscdn.lbdj.com/", envImgList: [], showPicker: !1, showImg: [], imgIndex: 0, imgShow: !1, images: [], videoUrl: "", isAudio: !1, audioUrl: "", materialsData: null, showToViewPopup: !1, showAssociationPopup: !1, timer: null, showToViewPopupContcont: "", total: "", explain: "", partsShow: !1, orderGoodsId: null, kdsProductList: [] } }, filters: { measurementUnit(e) { let t = ""; switch (e) { case "1": t = "-普通测量"; break; case "2": t = "-精细测量"; break; case "3": t = "-精细测量"; break; default: t = ""; break }return t } }, watch: { "orderInfo.isFirstQuery": { handler(e) { this.timer = null, clearTimeout(this.timer), e == 1 && (this.showAssociationPopup = !0, setTimeout(() => { this.showAssociationPopup = !1, clearTimeout(this.timer), this.timer = null }, 3e3)) }, immediate: !0, deep: !0 } }, created() { var e; this.isEnvironment(); for (let t = 0; t < this.orderInfo.goodsList.length; t++) { const s = [], i = this.orderInfo.goodsList[t]; for (let a = 0; a < i.goodsImg.length; a++)s.push(i.goodsImg[a]); if ((e = i.goodsEnvImg) != null && e.length) for (let a = 0; a < i.goodsEnvImg.length; a++)s.push(i.goodsEnvImg[a]); this.orderInfo.goodsList[t].goodsImgList = s } this.total = this.orderInfo.goodsList.reduce((t, s) => t += 1, 0), this.orderInfo.cid == 130 ? this.explain = this.orderInfo.catName + "-工程项目订单" : this.explain = this.orderInfo.catName + "-" + this.orderInfo.type, this.orderInfo, this.orderInfo.goodsList.forEach(t => { t.isOpen = !1, t.isOpenParts = !1 }), this.goRates(), this.orderInfo.goodsList[0].attach, this.setQueryInfo(), G("凯迪仕", this.orderInfo.busserId) && this.initKdsProductList() }, methods: { getGoodsMaterialinfo(e) { return e.reduce((s, i) => { const a = s.find(o => o.detailType === i.detailType); return a ? a.dataName += "/".concat(i.dataName) : s.push({ detailType: i.detailType, dataName: i.dataName }), s }, []) }, getMaterialLength(e) { return !!this.getGoodsMaterialinfo(e).filter(i => i.dataName.length > 7).length }, setItemOpen(e) { const { repairQuantity: t, goodsMaterialList: s } = e, i = this.getGoodsMaterialinfo(s || []), a = { repairQuantity: t, list: i }; this.$refs.serviceDetails.open(a), this.$forceUpdate() }, showToView(e) { this.showToViewPopupContcont = e, this.showToViewPopup = !0 }, ViewOrderVedio(e) { const t = this.$getBuriedPointOrderBaseData(); t.video_name = e }, goLook(e) { this.partsShow = !0, this.orderGoodsId = e }, openAudio(e) { this.audioUrl = e, setTimeout(() => { this.isAudio = !0 }, 300) }, lookVideo(e) { this.videoUrl = e, setTimeout(() => { if (window.navigator.userAgent.match(/Android/i)) if (this.isMiniwx) window.location.href = this.videoUrl; else { const t = { reqType: "GET_WEB_USER_PLAYVIDEO", data: { videoUrl: this.videoUrl } }; window.sendAppData(t) } else this.isMiniwx ? window.location.href = this.videoUrl : video.play() }, 300), this.ViewOrderVedio("") }, showImgs(e, t) { this.imgIndex = t, this.images = e, this.imgShow = !0 }, openImg(e, t) { this.imgIndex = t, this.showImg = this.envImgList[e].envImgUrlClient, this.showPicker = !0 }, isEnvironment() { const e = [], t = this.orderInfo.goodsList; for (let s = 0; s < t.length; s++)if (t[s].envImgUrlClient) { const i = JSON.parse(t[s].envImgUrlClient), a = []; if (i.length > 0) { for (let o = 0; o < i.length; o++)i[o].indexOf("http") == -1 ? a.push(this.imgUrl + i[o]) : a.push(i[o]); e.push({ goodsName: t[s].goodsName, envImgUrlClient: a, envImgNote: t[s].envImgNote }) } } this.envImgList = e }, goVideo(e) { if (e.isVideoList == 1) sessionStorage.setItem("orderVideoList", JSON.stringify(e.videoList)), this.$router.push({ path: "/orderHome/videoList", query: {} }); else if (e.azvideo) if (this.isMiniwx) window.location.href = e.azvideo; else { const t = { reqType: "GET_WEB_USER_PLAYVIDEO", data: { videoUrl: e.azvideo } }; window.sendAppData(t) } else if (e.ptAzvideo) if (this.isMiniwx) window.location.href = e.ptAzvideo; else { const t = { reqType: "GET_WEB_USER_PLAYVIDEO", data: { videoUrl: e.ptAzvideo } }; window.sendAppData(t) } this.ViewOrderVedio("") }, openShow(e) { this.title = e.labelContent, this.txt = e.defaultExplain, this.isShow = !0 }, goGood() { this.$router.push({ path: "/pages/2021/page0821", query: {} }) }, goRates() { const e = this, t = { firstCategoryId: this.orderInfo.cid, source: this.orderInfo.orderChannel, orderId: this.orderId }; h(t).then(s => { s.code == 200 ? this.materialsData = s.data : e.$toast(s.msg) }, function (s) { }) }, handleLook() { this.$router.push({ path: "/order/rates", query: { materialsData: JSON.stringify(this.materialsData) } }) }, setQueryInfo() { const { cid: e } = this.orderInfo; window.sessionStorage.setItem("compatibleFirstCategoryId", e) }, closeAssociationPopup() { this.showAssociationPopup = !1, clearTimeout(this.timer), this.timer = null }, async initKdsProductList() { const e = await L({ orderId: this.orderId }); if (e.code == 200) { const t = e.data; this.kdsProductList = t || [] } else this.$toast(e.msg) }, openLockInfoDialog() { this.$refs.lockInfoDialogRef.open() } } }; var ds = function () { var i; var t = this, s = t._self._c; return s("div", { staticClass: "main" }, [s("div", { staticClass: "new-padd new-main-2021" }, [t.orderInfo.returnStoreFeeTip ? s("div", { staticClass: "pj-hit" }, [t._v(" " + t._s(t.orderInfo.returnStoreFeeTip) + " ")]) : t._e(), s("div", { staticClass: "new-title" }, [s("div", { staticClass: "new-title-left" }, [t._v(" 服务信息 "), s("span", { staticClass: "explain" }, [t._v("(" + t._s(t.explain) + ")")]), t.orderInfo.isKdsTag ? s("img", { staticClass: "kds-logo", attrs: { src: "https://osscdn.lbdj.com/D/B/B-C/upload/order/2023-12-13-05-15-25/DBC4BCFCC5764CC9A22910AEAB36D2D4/logo.png" } }) : t._e(), t.orderInfo.releationMeasureInstall == 1 ? s("img", { staticClass: "icon-question", attrs: { src: "https://osscdn.lbdj.com/J/G/G-A/devCustom/20240906/jjb/icon_question.png" }, on: { click: function (a) { t.showAssociationPopup = !0 } } }) : t._e()]), t.orderInfo.orderStatus < 3 ? s("div", { staticClass: "total" }, [t._v("总数 " + t._s(t.orderInfo.totalGoodsNum))]) : t._e()]), s("van-divider"), s("div", { staticClass: "goods-box" }, t._l(t.orderInfo.goodsList, function (a, o) { var c, r, d, u, f; return s("div", { key: o, staticClass: "goods-box-item" }, [s("div", { staticClass: "media-list" }, [s("div", { staticClass: "list" }, [s("div", { staticClass: "img" }, [s("van-image", { attrs: { fit: "cover", src: a.goodsImgList[0] }, on: { click: function (l) { return t.showImgs(a.goodsImgList, 0) } } }), a.goodsImgList.length > 1 ? s("div", { staticClass: "disabled" }, [t._v(" 共" + t._s(a.goodsImgList.length) + "张 ")]) : t._e()], 1), a.orderServiceContent ? t._e() : s("div", { staticClass: "good-info" }, [(c = a.goodsMaterialList) != null && c.length ? [s("div", { staticClass: "threeCategoryName" }, [t._v(" " + t._s(a.threeCategoryName) + " ")]), a.repairQuantity ? s("div", { staticClass: "good-content omit" }, [s("div", { staticClass: "lable" }, [t._v("需维修处")]), s("div", { staticClass: "text" }, [t._v(t._s(a.repairQuantity) + "处")])]) : t._e(), t._l(t.getGoodsMaterialinfo(a.goodsMaterialList || []), function (l, v) { return s("div", { key: v, staticClass: "flex-box" }, [v <= 1 ? s("div", { staticClass: "good-content omit" }, [s("div", { staticClass: "lable" }, [t._v(t._s(l.detailType))]), s("div", { staticClass: "text" }, [t._v(t._s(l.dataName))])]) : t._e(), (a.goodsMaterialStrList.length > 2 || t.getMaterialLength(a.goodsMaterialList || [])) && v == 1 ? s("span", { staticClass: "open", on: { click: function (b) { return t.setItemOpen(a) } } }, [t._v("维修详情")]) : t._e()]) })] : [(t.orderInfo.cid == 136 && t.orderInfo.isCurtainsLcType || t.orderInfo.cid == 143) && t.orderInfo.type == "量尺" ? s("div", { staticClass: "good-content" }, [s("div", { staticClass: "threeCategoryName" }, [t._v(t._s(a.goodsName) + "*" + t._s(a.goodsNum))])]) : s("div", [s("div", { staticClass: "threeCategoryName" }, [t._v(" " + t._s(a.threeCategoryName) + "*" + t._s(a.goodsNum) + " ")]), s("div", { staticClass: "good-content" }, [s("span", [t._v("名称")]), t._v(" " + t._s(a.goodsName)), a.measurementFlag ? s("span", [t._v(t._s(t._f("measurementUnit")(a.measurementFlag)))]) : t._e()])]), (t.orderInfo.cid == 136 && t.orderInfo.isCurtainsLcType || t.orderInfo.cid == 143) && t.orderInfo.type == "量尺" ? s("div", { staticClass: "good-content" }, [s("span", [t._v("测量类型")]), t._v(" " + t._s(a.specName) + " ")]) : s("div", { staticClass: "good-content" }, [a.isAppliance && a.specModel ? s("div", [s("span", [t._v("型号/规格")]), t._v(" " + t._s(a.specModel) + " ")]) : s("div", [s("span", [t._v("规格")]), t._v(" " + t._s(t.orderInfo.cid == 129 ? "电桩功率" : "") + t._s(a.specName) + " ")])]), (t.orderInfo.cid == 136 && t.orderInfo.isCurtainsLcType || t.orderInfo.cid == 143) && t.orderInfo.type == "量尺" ? s("div", { staticClass: "good-content" }, [s("span", [t._v(t._s(t.orderInfo.cid == 143 && a.specName == "门类测量" ? "门数量" : "窗户数量"))]), t._v(" " + t._s(t.orderInfo.cid == 143 ? a.goodsNum : a.curtainquantity) + " ")]) : t._e()]], 2)])]), a.orderVideo || a.isVideoList == 1 || a.azvideo || a.ptAzvideo ? s("div", { staticClass: "goods-video" }, [a.orderVideo ? s("div", { staticClass: "video-item", on: { click: function (l) { return t.lookVideo(a.orderVideo) } } }, [s("img", { attrs: { src: "https://osscdn.lbdj.com/app-h5/img/new/video.png", alt: "" } }), s("span", [t._v(" 商品视频 ")]), s("van-icon", { attrs: { name: "arrow", size: "12px" } })], 1) : t._e(), a.isVideoList == 1 || a.azvideo || a.ptAzvideo ? s("div", { staticClass: "video-item", on: { click: function (l) { return t.goVideo(a) } } }, [s("img", { attrs: { src: "https://osscdn.lbdj.com/app-h5/img/new/video.png", alt: "" } }), s("span", [t._v(" 教程视频 ")]), s("van-icon", { attrs: { name: "arrow", size: "12px" } })], 1) : t._e()]) : t._e(), a.orderServiceContent ? s("div", { staticClass: "list-m" }, [s("div", { staticClass: "list-li" }), s("div", { staticClass: "list-li" }, [s("p", [s("span", [t._v("服务内容：")]), t._v(t._s(a.orderServiceContent))])])]) : s("div", { staticClass: "list-pt" }, [a.maintenanceItemsDto ? [s("div", { staticClass: "pt-two" }, [s("div", { staticClass: "co_bottom" }, [s("div", [t._v("维修内容：")]), s("div", [t._v(t._s(a.maintenanceItemsDto.repairContent))])])]), a.maintenanceItemsDto.repairRange ? s("div", { staticClass: "pt-two" }, [s("div", { staticClass: "co_bottom" }, [s("div", [t._v("维修范围：")]), s("div", { staticClass: "to_view", on: { click: function (l) { return t.showToView(a.maintenanceItemsDto.repairRange) } } }, [t._v(" 查看 ")])])]) : t._e()] : t._e(), a.incomingLineLength != null && a.incomingLineLength != "" ? s("div", { staticClass: "pt-two" }, [s("p", { staticClass: "pt-p" }, [s("span", [t._v("进线长度：")]), t._v(t._s(a.incomingLineLength) + "米")])]) : t._e(), a.materialRequirements != null && a.materialRequirements != "" ? s("div", { staticClass: "pt-two" }, [s("p", { staticClass: "pt-p" }, [s("span", [t._v("材料要求：")]), t._v(t._s(a.materialRequirements))])]) : t._e(), a.propertyRequirements != null && a.propertyRequirements != "" ? s("div", { staticClass: "pt-two" }, [s("p", { staticClass: "pt-p" }, [s("span", [t._v("物业要求：")]), t._v(t._s(a.propertyRequirements))])]) : t._e(), a.wxType != null && a.wxType != "" ? s("div", { staticClass: "pt-two" }, [s("p", { staticClass: "pt-p" }, [s("span", [t._v("维修类型：")]), t._v(t._s(a.wxType))])]) : t._e(), a.goodsSize != null && a.goodsSize != "" ? s("div", { staticClass: "pt-two" }, [s("p", { staticClass: "pt-p" }, [s("span", [t._v("商品尺寸：")]), t._v(t._s(a.goodsSize))])]) : t._e(), a.goodsQuality != null && a.goodsQuality != "" ? s("div", { staticClass: "pt-two" }, [s("p", { staticClass: "pt-p" }, [s("span", [t._v("商品材质：")]), t._v(t._s(a.goodsQuality))])]) : t._e(), a.returnStoreName ? s("div", { staticClass: "pt-two" }, [s("p", { staticClass: "pt-p" }, [s("span", [t._v("配件返厂：")]), t._v(t._s(a.returnStoreName))])]) : t._e(), a.goodsBrand ? s("div", { staticClass: "pt-two" }, [s("p", { staticClass: "pt-p" }, [s("span", [t._v("商品品牌：")]), t._v(t._s(a.goodsBrand))])]) : t._e(), a.isReturnStore ? s("div", { staticClass: "pt-two" }, [s("p", { staticClass: "pt-p" }, [s("span", [t._v("需配件返厂：")]), s("span", { staticStyle: { color: "#4385f5" }, on: { click: function (l) { return t.goLook(a.orderGoodsId) } } }, [t._v("查看详情")]), s("van-icon", { staticStyle: { "vertical-align": "-1px" }, attrs: { name: "arrow", size: "12px", color: "#8e8e93" } })], 1)]) : t._e(), a.labelList.length > 0 ? s("div", { staticClass: "pt-list" }, [s("div", { staticClass: "title" }, [t._v("商品备注：")]), s("div", { staticClass: "tab" }, t._l(a.labelList, function (l, v) { return s("p", { key: v, on: { click: function (b) { return t.openShow(l) } } }, [t._v(" " + t._s(l.labelContent) + " ")]) }), 0)]) : t._e()], 2), (r = a.homeSurchargeList) != null && r.length ? t._l(a.homeSurchargeList, function (l) { return s("div", { staticClass: "sur-txt" }, [s("div", { staticClass: "left-txt" }, [s("span", [t._v(t._s(l.surchargeName))]), s("span", [t._v("*" + t._s(l.num))])]), s("div", { staticClass: "r-txt" }, [t._v(t._s(l.parameterName))])]) }) : t._e(), a.installRequire || a.descripation || a.isReturnStore || a.attach || a.addFeeRemark ? s("div", { staticClass: "goods-pj" }, [s("div", { staticClass: "title" }, [t._v(" " + t._s((d = a.goodsMaterialList) != null && d.length ? "故障描述" : "特殊要求")), s("span", [t._v("（请仔细阅读、严格按照要求服务）")])]), s("van-divider"), s("div", { staticClass: "mt" }, [a.installRequire ? s("div", [a.installRequire ? s("div", { directives: [{ name: "dompurify-html", rawName: "v-dompurify-html", value: a.installRequire, expression: "item.installRequire" }], staticClass: "p", staticStyle: { "word-break": "break-all" } }) : t._e()]) : t._e(), a.attach ? t._l(JSON.parse(a.attach), function (l, v) { return s("span", { key: v, staticClass: "install-goods-box" }, [s("span", { staticClass: "install-name" }, [t._v(t._s(l.addFeeName) + "：")]), l.addFeeStyles == 1 ? s("span", { staticClass: "install-info" }, [t._v(t._s(l.addFeeValue))]) : t._e(), l.addFeeStyles == 2 ? s("span", { staticClass: "install-info" }, [t._v(t._s(l.addFeeValue))]) : t._e(), l.addFeeStyles == 3 && l.quantity > 0 ? s("span", { staticClass: "install-info" }, [t._v(" *" + t._s(l.quantity) + " ")]) : t._e(), s("br")]) }) : t._e(), a.addFeeRemark ? s("span", { staticClass: "install-name" }, [s("span", { staticClass: "install-info" }, [t._v(t._s(a.addFeeRemark))])]) : t._e(), a.descripation && a.labelList.length < 1 ? s("span", { staticClass: "install-name" }, [(u = a.goodsMaterialList) != null && u.length ? t._e() : s("span", { staticClass: "install-name" }, [t._v("商品备注：")]), s("span", { staticClass: "install-info" }, [t._v(t._s(a.descripation))])]) : t._e()], 2)], 1) : t._e(), (f = a.goodsPartsList) != null && f.length ? s("div", { staticClass: "parts-card", staticStyle: { margin: "10px 0 0 0" } }, [s("div", { staticClass: "title" }, [t._v("所需配件")]), t._l(a.goodsPartsList, function (l, v) { return s("div", { key: v, staticClass: "flex-box" }, [v <= 2 ? s("div", { staticClass: "good-content omit-long" }, [s("div", { staticClass: "lable" }, [t._v(t._s(l.dataName))]), s("div", { staticClass: "text" }, [t._v(t._s(l.dataNum) + "个")])]) : t._e()]) })], 2) : t._e(), t.orderInfo.cid == 130 ? s("div", { staticClass: "goods-gc", staticStyle: { "padding-bottom": "0.2rem" } }, [s("div", { staticClass: "gc-ul" }, [a.dismantle === 0 || a.dismantle == 1 ? s("p", [t._v(" 是否需要拆旧：" + t._s(a.dismantle == 1 ? "是" : "否") + " ")]) : t._e(), a.accessories === 0 || a.accessories == 1 ? s("p", [t._v(" 是否包含辅料：" + t._s(a.accessories == 1 ? "是" : "否") + " ")]) : t._e(), a.elevator === 0 || a.elevator == 1 ? s("p", [t._v(" 是否有电梯：" + t._s(a.elevator == 1 ? "是" : "否") + " ")]) : t._e(), a.wiring === 0 || a.wiring == 1 ? s("p", [t._v(" 是否需要布线：" + t._s(a.wiring == 1 ? "是" : "否") + " ")]) : t._e(), a.changeLock === 0 || a.changeLock == 1 ? s("p", [t._v(" 是否需要换锁：" + t._s(a.changeLock == 1 ? "是" : "否") + " ")]) : t._e()]), a.projectRequire ? s("van-divider") : t._e(), a.projectRequire ? s("div", { staticClass: "gc-p" }, [s("span", [t._v("施工要求说明：")]), t._v(t._s(a.projectRequire) + " ")]) : t._e()], 1) : t._e(), a.voiceRemark ? s("div", { staticClass: "audio" }, [s("p", [t._v("语音备注：")]), s("div", { staticClass: "audio-box", on: { click: function (l) { return t.openAudio(a.voiceRemark) } } }, [s("img", { attrs: { src: "https://osscdn.lbdj.com/app-h5/img/new/voice-icon.png", alt: "" } }), t._v(t._s(a.voiceDuration) + "s ")])]) : t._e(), a.tipContent ? s("div", { staticClass: "airCrText" }, [t._v(" " + t._s(a.tipContent) + " "), t.orderInfo.cid == 148 && t.materialsData && (t.orderInfo.servicetype == "wx" || t.orderInfo.servicetype == "anz") ? s("span", { staticClass: "see", on: { click: t.handleLook } }, [t._v("查看 ")]) : t._e()]) : t._e(), a.banAddFeeName ? s("div", { staticClass: "refitTip" }, [t._v(" 订单费用包改装提示：该商品已包含" + t._s(a.banAddFeeName) + "，请勿针对此类费用申请增加费用，且禁止向客户线下收取 ")]) : t._e()], 2) }), 0), (i = t.kdsProductList) != null && i.length ? s("div", { staticClass: "kds-product-row" }, [s("div", { staticClass: "title" }, [t._v("门锁测量信息")]), s("div", { staticClass: "btn", on: { click: t.openLockInfoDialog } }, [t._v("查看详情"), s("van-icon", { attrs: { name: "arrow" } })], 1)]) : t._e()], 1), t.envImgList.length > 0 ? s("div", { staticClass: "environment new-padd" }, [t._m(0), s("div", { staticClass: "env-list" }, t._l(t.envImgList, function (a, o) { return s("div", { key: o, staticClass: "list-li" }, [s("div", { staticClass: "list-title" }, [t._v(t._s(a.goodsName))]), s("div", { staticClass: "list-img" }, t._l(a.envImgUrlClient, function (c, r) { return s("img", { key: r, attrs: { src: c + "?x-oss-process=image/resize,m_fill,h_110,w_110,limit_0" }, on: { click: function (d) { return t.openImg(o, r) } } }) }), 0), a.envImgNote ? s("div", { staticClass: "list-txt" }, [t._v("环境备注：" + t._s(a.envImgNote))]) : t._e()]) }), 0)]) : t._e(), t.isAudio ? s("div", [s("van-overlay", { attrs: { show: "true" } }, [s("div", { staticClass: "wrapper" }, [s("audio", { ref: "au", attrs: { src: t.audioUrl, controls: "controls" } }, [t._v(" Your browser does not support the audio element. ")])]), s("div", { staticClass: "i" }, [s("van-icon", { attrs: { name: "clear" }, on: { click: function (a) { t.isAudio = !1 } } })], 1)])], 1) : t._e(), s("van-dialog", { attrs: { title: t.title, message: t.txt, "confirm-button-text": "关闭", "confirm-button-color": "#FE4932" }, on: { confirm: function (a) { t.isShow = !1 } }, model: { value: t.isShow, callback: function (a) { t.isShow = a }, expression: "isShow" } }), s("van-image-preview", { attrs: { images: t.showImg, "start-position": t.imgIndex, closeable: !0 }, model: { value: t.showPicker, callback: function (a) { t.showPicker = a }, expression: "showPicker" } }), s("van-image-preview", { attrs: { images: t.images, "start-position": t.imgIndex, closeable: !0 }, model: { value: t.imgShow, callback: function (a) { t.imgShow = a }, expression: "imgShow" } }), t.videoUrl ? s("div", { staticClass: "video-box" }, [s("video", { attrs: { id: "video", "x5-playsinline": "", playsinline: "", "webkit-playsinline": "", preload: "auto", controls: "controls", src: t.videoUrl } })]) : t._e(), s("van-popup", { staticClass: "show_toview", model: { value: t.showToViewPopup, callback: function (a) { t.showToViewPopup = a }, expression: "showToViewPopup" } }, [s("div", { staticClass: "show_toview_box" }, [s("div", { staticClass: "show_toview_box_top" }, [t._v("维修范围")]), s("div", { staticClass: "show_toview_box_cont" }, [t._v(t._s(t.showToViewPopupContcont))]), s("div", { staticClass: "show_toview_box_bottom", on: { click: function (a) { t.showToViewPopup = !1 } } }, [t._v("我知道了")])])]), s("van-popup", { staticClass: "show_toview", model: { value: t.showAssociationPopup, callback: function (a) { t.showAssociationPopup = a }, expression: "showAssociationPopup" } }, [s("div", { staticClass: "association-wrapper" }, [s("div", { staticClass: "association-title" }, [t._v("测量+安装关联单")]), s("div", { staticClass: "association-content" }, [t._v(" 该订单商家要求由同一师傅完成测量+安装，后续如有安装单将自动派单给你，如无法承接安装服务请慎重接单。 ")]), s("div", { staticClass: "association-bottom", on: { click: t.closeAssociationPopup } }, [t._v("我知道了")])])]), s("parts-dialog", { attrs: { id: t.orderGoodsId, "order-info": t.orderInfo, show: t.partsShow }, on: { "update:show": function (a) { t.partsShow = a } } }), s("service-details", { ref: "serviceDetails" }), s("lock-info-dialog", { ref: "lockInfoDialogRef", attrs: { kdsProductList: t.kdsProductList } })], 1) }, vs = [function () { var e = this, t = e._self._c; return t("div", { staticClass: "new-titles" }, [t("div", { staticClass: "new-ti" }, [e._v("安装环境")])]) }], _s = n(cs, ds, vs, !1, null, "efa2a44e", null, null); const zs = _s.exports; const ps = { name: "HelloWorld", data() { return { title: "这是一个测试的数据标题", show: !1 } }, created() { }, methods: { showpop() { this.show = !0 } } }; var hs = function () { var t = this, s = t._self._c; return s("div", { staticClass: "main" }, [s("div", { staticClass: "new-padd new-main-2021", on: { click: t.showpop } }, [t._m(0), s("van-icon", { attrs: { name: "arrow", size: "16px", color: "#8e8e93" } })], 1), s("van-action-sheet", { attrs: { title: "当日达说明" }, model: { value: t.show, callback: function (i) { t.show = i }, expression: "show" } }, [s("div", { staticClass: "content" }, [t._v(" 根据预计到货时问，若15:00前到货，在当天进行上门完成服务即可获得额外的当日达补贴10元；若15:00之后到货在次日12:00前进行上门完成服务可获得额外的当日达补贴10元，请合理安排上门服务时间。 ")])])], 1) }, us = [function () { var e = this, t = e._self._c; return t("div", { staticClass: "sub-p" }, [t("div", { staticClass: "label" }, [e._v("当日达")]), t("div", [e._v("按时上门可获得10元补贴")])]) }], fs = n(ps, hs, us, !1, null, "18e0bb98", null, null); const Ws = fs.exports; const gs = { props: ["orderInfo"] }; var ms = function () { var t = this, s = t._self._c; return t.orderInfo.wyServiceNoticeContent ? s("div", { staticClass: "main" }, [s("div", { staticClass: "new-padd new-main-2021" }, [t._m(0), s("van-divider"), s("div", { staticClass: "lz-install-content" }, [t._v(" " + t._s(t.orderInfo.wyServiceNoticeContent) + " ")])], 1)]) : t._e() }, Cs = [function () { var e = this, t = e._self._c; return t("div", { staticClass: "new-title" }, [t("div", { staticClass: "new-title-left" }, [e._v("量尺并安装服务须知")])]) }], ws = n(gs, ms, Cs, !1, null, "be860fb2", null, null); const Hs = ws.exports; const ys = { name: "repairDescription", props: ["orderInfo"], data() { return { deadMin: 0, timer: null } }, created() { }, methods: {} }; var Ds = function () { var t = this, s = t._self._c; return s("div", { staticClass: "box" }, [s("div", { staticClass: "title" }, [t._v("检修说明")]), s("div", { staticClass: "txt" }, [t._v(t._s(t.orderInfo.overhaulDescription))]), s("div", { staticClass: "content" }, [t.orderInfo.originalWorkerFlag == 1 ? s("div", { staticClass: "item" }, [s("p", [t._v("1、原订单号：" + t._s(t.orderInfo.originalOrderSn) + "，需要二次上门维修")]), s("p", [t._v("2、请师傅上门后安装维修直至将产品服务至正常使用")]), s("p", [t._v("3、若师傅拒绝上门则会受到平台【拒绝二次上门】的处罚条例")])]) : s("div", { staticClass: "item" }, [s("p", [t._v(" 1、检修单，上门检测确认问题后，请务必联系商家/客服确认是否继续维修，以及协商费用达成一致后再现场申请费用，否则平台不予处理 ")]), s("p", [t._v("2、上门费以及工钱在提交检测结果时申请。")])])])]) }, bs = [], ks = n(ys, Ds, bs, !1, null, "9f3af04d", null, null); const Qs = ks.exports; export { qs as A, Rs as a, Es as b, ut as c, Ms as d, Hs as e, Qs as f, zs as g, Bs as h, D as i, dt as j, js as l, Ws as n, Gs as o, Vs as r, Us as s };

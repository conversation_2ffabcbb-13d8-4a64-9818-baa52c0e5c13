
import{n,a1 as i}from"./index-6623cdb9.js";const d={name:"",props:{orderInfo:{type:Object,required:!1,defualt:()=>({})},title:{type:String,required:!1,default:""},useSimple:{type:Boolean,default:!1}},data(){return{}},computed:{},watch:{},created(){},mounted(){},methods:{goToService(){let r=0;(this.orderInfo.isOfflineOrder==1||this.orderInfo.isDoorStore==1)&&(r=1);let e="";this.orderInfo.goodsList[0]&&(e=this.orderInfo.goodsList[0].goodsName+"*"+this.orderInfo.goodsList[0].goodsNum+" "+this.orderInfo.goodsList[0].specName);const o="订单编号："+this.orderInfo.orderNum+"\n订单类型："+this.orderInfo.catName+this.orderInfo.type+"\n客户姓名："+this.orderInfo.customerName+"\n客户电话："+this.orderInfo.customerPhone+"\n客户地址："+this.orderInfo.province+this.orderInfo.city+this.orderInfo.district+this.orderInfo.address+"\n商品信息："+e,t={reqType:"TO_CUSTOMER_SERVICE",data:{orderId:this.orderInfo.orderNum,description:o,isOffline:r==1?r:0,serviceToken:this.orderInfo.serviceToken}},s=this.orderInfo.serviceToken;i.prototype.isMiniwx?(wx.miniProgram.postMessage({data:t}),this.$goMiniPage("/pages/index/placeholder/index?sendObj=".concat(o,"&serviceToken=").concat(s))):(window.sendAppData(t),this.ContactCustomerService())},ContactCustomerService(){this.title||document.title}}};var a=function(){var e=this,o=e._self._c;return o("div",{class:{"customer-service-component":!e.useSimple},on:{click:e.goToService}},[e._t("default",function(){return[o("img",{staticClass:"avatar",attrs:{src:"https://osscdn.lbdj.com/lbdj_app_h5/newUi/kefu.png"}})]})],2)},c=[],f=n(d,a,c,!1,null,"23beb63b",null,null);const m=f.exports;export{m as c};

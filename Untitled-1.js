HTTP/2 200
date: Sun, 14 Sep 2025 05:20:16 GMT
content-type: application/javascript
vary: Accept-Encoding
last-modified: Thu, 11 Sep 2025 03:15:03 GMT
etag: W/"68c23eb7-14b0"
content-encoding: br

import{ah as o,hQ as r,av as c,bI as n,n as d}from"./index-6623cdb9.js";import{s as l}from"./activity-f0e1a07d.js";const p={name:"LbdjAppH5FeedbackDialog",props:{orderInfo:{type:[Object,String],default:()=>{}},value:{type:Boolean,default:!1},quotedAwardHint:{type:String,default:""}},computed:{...o(["isContainSpeedUp"]),show:{get(){return this.value},set(e){e||this.$emit("input",!1)}}},data(){return{orderInfos:{pageSize:3,pageNum:1,orderId:""},showChunjieAd:!1,succData:[],popDto:"",total:0,switchFlag:!1}},created(){this.workerOrderRebate(this.orderInfo.id),this.getSpringInfo(),this.getAutoOffer()},methods:{async getAutoOffer(){const e=await r({});e.code==200&&(this.switchFlag=e.data.switchFlag)},getSpringInfo(){l({}).then(e=>{e.code==200&&e.data?this.showChunjieAd=e.data.result==1:this.showChunjieAd=!1})},async workerOrderRebate(e){const{ordertype:t}=JSON.parse(sessionStorage.getItem("orderInfo"));c({orderId:e,clickSource:[0,"0",1,"1"].includes(t)?t:null}).then(s=>{s.code==="200"&&s.data&&s.data.popDto&&(this.popDto=s.data.popDto)})},goback(){this.$emit("input",!1),this.$emit("goback"),this.$router.go(0)},overlay(){this.$emit("update:show",!1)},open(){this.orderInfos.orderId=this.orderInfo.id,this.getNearOrderPushApiRequest()},async getNearOrderPushApiRequest(){this.$toast.loading({duration:0,forbidClick:!0,loadingType:"spinner",message:"加载中..."});const{code:e,data:t,total:s}=await n(this.orderInfos).catch(a=>{this.$toast.clear()});this.succData=["1231"],e==200&&t?(this.succData=t,this.total=s,this.$toast.clear()):this.$toast.clear()},reFresh(){if(this.total>0){const e=this.total/3;this.orderInfos.pageNum>=e?this.orderInfos.pageNum=1:this.orderInfos.pageNum+=1}this.getNearOrderPushApiRequest()},retureOrderHall(){if(this.isMiniwx){wx.miniProgram.postMessage({data:{back:1}});const e="/packageB/pages/order/order-list/order-list?activeIndex=2&back=1";this.$goMiniPage(e,!1,!1)}else{const e={reqType:"TO_PAGE",data:{appPage:2,description:"去报价"}};window.sendAppData(e)}},snatchOrders(e){this.$router.push({path:"/order/details",query:{orderId:e.id,orderType:e.orderType,orderStatus:e.orderstatus,clickSource:e.grabStatus=="立即抢单"?0:1}}),this.$router.go(0)},toChunjieAd(){this.$router.push({path:"/act/chunjieSubsidy"})}}};var h=function(){var t=this,s=t._self._c;return s("van-popup",{attrs:{position:"bottom","close-on-click-overlay":!1},on:{open:t.open,"click-overlay":t.overlay},model:{value:t.show,callback:function(a){t.show=a},expression:"show"}},[s("div",{staticClass:"content"},[s("div",{staticClass:"content-item"},[s("div",{staticClass:"quota-succ-page"},[s("div",{staticClass:"succ-head"},[s("div",{staticClass:"head-main"},[s("img",{staticClass:"succ-icon",attrs:{src:"https://osscdn.lbdj.com/worker-h5/img/succ-icon.png",alt:""}}),s("p",{staticClass:"head-txt"},[t._v("报价成功")]),t.quotedAwardHint?s("p",{staticClass:"head-info"},[t._v(t._s(t.quotedAwardHint))]):t._e()])]),t.popDto?s("div",{staticClass:"rebate-box"},[s("div",{staticClass:"label"},[t._v("返佣")]),s("div",{staticClass:"rebate-item"},[s("div",{staticStyle:{"margin-bottom":"5px"}},[s("span",[t._v(t._s(t.popDto.lastTaskTitle))]),t.popDto.lastTaskRebateRate?s("span",[t._v(t._s(t.popDto.lastTaskRebateRate)+"%")]):t._e()]),s("div",[t._v(t._s(t.popDto.taskName)+"可获得返佣"+t._s(t.popDto.rebateRate)+"%")])])]):t._e(),t.showChunjieAd&&t.isContainSpeedUp?s("div",{staticClass:"chunjie-ad",on:{click:t.toChunjieAd}}):t._e(),s("div",{staticClass:"content-main"},[s("div",{staticClass:"content-title"},[t.succData&&t.succData.length?s("div",{staticClass:"title-box"},[t._v("附近订单")]):t._e(),t.total>3?s("div",{staticClass:"refresh",on:{click:t.reFresh}},[s("img",{staticClass:"resresh-icon",attrs:{src:"https://osscdn.lbdj.com/lbdj_app_h5/comm/icons/load-icon.png",alt:""}}),s("p",{staticClass:"change"},[t._v("换一批")])]):t._e()]),s("div",{staticClass:"main-list"},t._l(t.succData,function(a,i){return s("div",{key:i,staticClass:"list-box",on:{click:function(v){return t.snatchOrders(a)}}},[s("div",{staticClass:"list-img"},[s("img",{attrs:{src:a.goodsImg,alt:""}})]),s("div",{staticClass:"list-right"},[s("div",{staticClass:"list-name"},[t._v(t._s(a.goodsInfo))]),s("div",{staticClass:"list-address"},[t._v(t._s(a.address))]),s("div",{staticClass:"list-location"},[s("div",{staticClass:"list-location-left"},[t._v("距本单上门地址："+t._s(a.distance)+"m")]),s("div",{staticClass:"list-location-right"},[a.orderMoneyStr.indexOf("报价中")!=-1?s("div",[t._v(" 去报价"),s("van-icon",{staticStyle:{"vertical-align":"-2px"},attrs:{name:"arrow"}})],1):s("div",[t._v(" "+t._s("￥"+a.orderMoneyStr)+" ")])])])])])}),0)])])]),s("div",{staticClass:"set-offer"},[s("div",{staticClass:"left"},[s("span",[t._v("提升报价效率，不再担心错过订单")])]),t.switchFlag?s("div",{staticClass:"right",on:{click:function(a){return t.$router.push({path:"/order/autoOffer"})}}},[s("span",[t._v("去设置")])]):t._e()]),s("div",{staticClass:"offers-bottom"},[s("div",{staticClass:"offers-btn btn-left",on:{click:t.goback}},[t._v("返回订单详情")]),s("div",{staticClass:"offers-btn btn-rights",on:{click:t.retureOrderHall}},[t._v("前往接单大厅")])])])])},u=[],f=d(p,h,u,!1,null,"21727fa4",null,null);const C=f.exports;export{C as default};

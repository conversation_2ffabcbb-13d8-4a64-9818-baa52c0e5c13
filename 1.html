<!--
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: maxin
 * @Date: 2022-07-21 20:55:25
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-06-03 09:51:31
-->
<!DOCTYPE html>
<html style="height: 100%">
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="renderer" content="webkit" />
        <meta
            name="viewport"
            content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover"
        />
        <meta name="format-detection" content="telephone=yes" />
        <meta http-equiv="pragma" content="no-cache" />
        <meta http-equiv="Cache-Control" content="no-cache" />
        <meta http-equiv="expires" content="0" />
        <title>鲁班到家</title>
        <link rel="stylesheet" href="https://osscdn.lbdj.com/lbdj_app_h5/js/index.css" />
        <script>
            window.ENV_CONFIG = {
                BASIC_API: '/lbdj/app/'
            }

            try {
                if (localStorage.getItem('ENV_CONFIG')) {
                    window.ENV_CONFIG = JSON.parse(localStorage.getItem('ENV_CONFIG'))
                }
            } catch (e) {
                console.error(e)
            }

            var s = document.createElement('script')
            s.src = '/public/config.js?v=' + new Date().getTime()

            s.onload = function () {
                setTimeout(() => {
                    localStorage.setItem('ENV_CONFIG', JSON.stringify(ENV_CONFIG))
                })
            }

            document.head.prepend(s)
        </script>
        <script type="text/javascript">
            /**
             * YDUI 可伸缩布局方案
             * rem计算方式：设计图尺寸px / 100 = 实际rem  例: 100px = 1rem
             */
            !(function (window) {
                /* 设计图文档宽度 */
                var docWidth = 750

                var doc = window.document,
                    docEl = doc.documentElement,
                    resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize'

                var recalc = (function refreshRem() {
                    var clientWidth = docEl.getBoundingClientRect().width

                    docEl.style.fontSize = 100 * (clientWidth / docWidth) + 'px'

                    return refreshRem
                })()

                /* 添加倍屏标识，安卓倍屏为1 */
                docEl.setAttribute(
                    'data-dpr',
                    window.navigator.appVersion.match(/iphone/gi) ? window.devicePixelRatio : 1
                )

                if (/iP(hone|od|ad)/.test(window.navigator.userAgent)) {
                    /* 添加IOS标识 */
                    doc.documentElement.classList.add('ios')
                    /* IOS8以上给html添加hairline样式，以便特殊处理 */
                    if (parseInt(window.navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/)[1], 10) >= 8)
                        doc.documentElement.classList.add('hairline')
                }

                if (!doc.addEventListener) return
                window.addEventListener(resizeEvt, recalc, false)
                doc.addEventListener('DOMContentLoaded', recalc, false)
            })(window)
        </script>
        <script>
            var _hmt = _hmt || []
            ;(function () {
                var hm = document.createElement('script')
                hm.src = 'https://hm.baidu.com/hm.js?0a0557e9ee345c3a8a84a78387287708'
                var s = document.getElementsByTagName('script')[0]
                s.parentNode.insertBefore(hm, s)
            })()
        </script>
      <script type="module" crossorigin src="/assets/index-6623cdb9.js"></script>
      <link rel="stylesheet" href="/assets/index-5be45467.css">
      <script type="module">import.meta.url;import("_").catch(()=>1);(async function*(){})().next();if(location.protocol!="file:"){window.__vite_is_modern_browser=true}</script>
      <script type="module">!function(){if(window.__vite_is_modern_browser)return;console.warn("vite: loading legacy chunks, syntax error above and the same error below should be ignored");var e=document.getElementById("vite-legacy-polyfill"),n=document.createElement("script");n.src=e.src,n.onload=function(){System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))},document.body.appendChild(n)}();</script>
    </head>

    <body>
        <div id="app"></div>
        <!-- <script type="text/javascript" src="https://gosspublic.alicdn.com/aliyun-oss-sdk-4.4.4.min.js"></script> -->
        <script src="https://osscdn.lbdj.com/lbdj_app_h5/js/jquery-3.7.1.min.js"></script>
        <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.4.0.js"></script>
        <script type="text/javascript" src="/js/aliyun-sdk.min.js"></script>
        <script type="text/javascript" src="/js/AMap-1.4.15.js"></script>
        <!-- 引入 Vue 和 Vant 的 JS 文件 -->
        
        
        <!-- <script src="http://pv.sohu.com/cityjson?ie=utf-8"></script> -->
      <script nomodule>!function(){var e=document,t=e.createElement("script");if(!("noModule"in t)&&"onbeforeload"in t){var n=!1;e.addEventListener("beforeload",(function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute("nomodule")||!n)return;e.preventDefault()}),!0),t.type="module",t.src=".",e.head.appendChild(t),t.remove()}}();</script>
      <script nomodule crossorigin id="vite-legacy-polyfill" src="assets/polyfills-legacy-9fad2ff5.js"></script>
      <script nomodule crossorigin id="vite-legacy-entry" data-src="assets/index-legacy-65e0ddcd.js">System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))</script>
    </body>
</html>

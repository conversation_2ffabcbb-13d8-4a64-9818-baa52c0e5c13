import requests
from urllib.parse import quote
import json

# 配置
url = "https://courseapi.ulearning.cn/users/login/v2"

# 请求头 - 简化版本，保留关键头部
headers = {
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36",
    "Accept": "application/json, text/plain, */*",  # 简化Accept头
    "Content-Type": "application/x-www-form-urlencoded",
    "Origin": "https://umooc.ulearning.cn",
    "Referer": "https://umooc.ulearning.cn/",
    "Accept-Language": "zh-CN,zh;q=0.9",
}

# 用户信息JSON - 正确编码
user_info_json = {
    "orgName": "武汉工商学院",
    "orgLogo": "https://leicloud.ulearning.cn/resource/3277/591257/6d49103f-0a4c-4ac1-8ba9-c8a78e4263b1.png",
    "roleId": 9,
    "sex": "0",
    "orgHome": "whgs.ulearning.cn",
    "userId": 13542305,
    "orgId": 3277,
    "authorization": "DD4BAD53E024A7DC79B775B6D57AD0D9",
    "studentId": "24206101",
    "loginName": "wtbu24206101",
    "name": "秦甜甜",
    "uversion": 2
}

# 将JSON转换为字符串并URL编码
user_info_str = json.dumps(user_info_json, ensure_ascii=False, separators=(',', ':'))
user_info_encoded = quote(user_info_str, safe='')

# Cookies - 使用正确编码的用户信息
cookies = {
    "loginFrom": "https%3A%2F%2Fumooc.ulearning.cn%2Fpc.html%23%2Flogin",
    "AUTHORIZATION": "DD4BAD53E024A7DC79B775B6D57AD0D9",
    "token": "DD4BAD53E024A7DC79B775B6D57AD0D9",
    "lms_user_org": "3277",
    "USER_INFO": user_info_encoded,
    "USERINFO": user_info_encoded,
    "lang": "zh",
    "ROOT_ORIGIN": "www.ulearning.cn",
    "_abfpc": "5a9f3122e2cf111ae2ec562734f25708d934a775_2.0",
    "cna": "6536ef29902f7af47f4e28c7c3de1615",
    "isClassroom": "1",
    "belong": "null",
    "xn_dvid_kf_20125": "08E8CC-E785248E-58F1-73D7-BD56-C42DBD2FCE7B",
    "xn_sid_kf_20125": "1759932956181824",
}

# 登录数据
data = {
    "loginName": "wtbu24206101",
    "password": "qttQTT20030526",
}

# 创建会话以保持连接
session = requests.Session()

print("=== 修复后的请求信息 ===")
print(f"URL: {url}")
print(f"Headers: {json.dumps(headers, indent=2, ensure_ascii=False)}")
print(f"Data: {data}")
print(f"User Info Encoded: {user_info_encoded[:100]}...")
print("\n=== 发送请求 ===")

try:
    # 发送请求
    response = session.post(
        url, 
        headers=headers, 
        cookies=cookies, 
        data=data, 
        timeout=30,
        allow_redirects=True
    )
    
    response.encoding = 'utf-8'
    
    # 输出响应信息
    print(f"状态码: {response.status_code}")
    print(f"响应URL: {response.url}")
    print(f"响应Cookies: {dict(response.cookies)}")
    print(f"响应头: {dict(response.headers)}")
    print(f"响应内容: {response.text}")
    
    # 分析响应
    if response.status_code == 200:
        try:
            json_response = response.json()
            print(f"\n✅ JSON响应: {json.dumps(json_response, indent=2, ensure_ascii=False)}")
            
            if json_response.get('success') or json_response.get('code') == 200:
                print("\n✅ 登录成功!")
            else:
                print(f"\n❌ 登录失败: {json_response.get('message', '未知错误')}")
                
        except json.JSONDecodeError:
            if "success" in response.text.lower() or "登录成功" in response.text:
                print("\n✅ 登录可能成功 (非JSON响应)")
            else:
                print("\n❌ 登录可能失败 (非JSON响应)")
    else:
        print(f"\n❌ HTTP错误: {response.status_code}")
        
except requests.exceptions.Timeout:
    print("❌ 请求超时")
except requests.exceptions.ConnectionError:
    print("❌ 连接错误")
except requests.exceptions.RequestException as e:
    print(f"❌ 请求异常: {e}")
except Exception as e:
    print(f"❌ 其他错误: {e}")

print("\n=== 问题分析 ===")
print("原始代码可能存在的问题:")
print("1. 重复导入requests模块")
print("2. Cookie中的中文字符编码不正确")
print("3. 缺少超时设置")
print("4. 缺少异常处理")
print("5. 请求头过于复杂，可能被服务器识别为爬虫")
print("6. 没有使用Session保持连接状态")
